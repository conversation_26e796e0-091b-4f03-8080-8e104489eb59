<template>
  <div class="template-management-container">
    <div class="filter-container">
      <div class="left">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="全部" name="all" />
          <el-tab-pane label="我管理的" name="mine" />
        </el-tabs>
      </div>
      <div class="right">
        <el-input
          v-model="searchQuery"
          placeholder="请输入内容"
          style="width: 200px"
          class="filter-item"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="filteredTemplateList"
      border
      stripe
      style="width: 100%"
    >
      <el-table-column prop="templateName" label="模板名称" min-width="180">
        <template #default="{ row }">
          <el-link type="primary" @click="goToDetail(row)">{{ row.templateName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="teamName" label="团队" min-width="120" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="updateTime" label="上次修改" min-width="150" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="row.templateName !== '默认模板'"
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.templateName !== '默认模板'"
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建模板对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '创建模板' : '编辑模板'"
      width="600px"
      destroy-on-close
    >
      <template-form
        :initial-data="currentTemplate"
        @submit="handleFormSubmit"
        @cancel="dialogVisible = false"
      />
    </el-dialog>

    <!-- 添加模板按钮 -->
    <el-button
      type="primary"
      class="add-button"
      @click="handleCreate"
    >
      创建模板
    </el-button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTemplateList, createTemplate, updateTemplate, deleteTemplate } from '@/api/templateManagement'
import TemplateForm from './components/TemplateForm.vue'
import { useRouter } from 'vue-router'

// 定义组件名称，解决linter警告
defineOptions({
  name: 'TemplateManagement'
})

const router = useRouter()

// 数据
const loading = ref(false)
const templateList = ref([])
const activeTab = ref('all')
const searchQuery = ref('')
const dialogVisible = ref(false)
const dialogType = ref('create') // 'create' 或 'edit'
const currentTemplate = ref({
  templateName: '',
  teamId: '',
  description: ''
})

// 计算过滤后的模板列表
const filteredTemplateList = computed(() => {
  let list = templateList.value

  // 根据标签筛选
  if (activeTab.value === 'mine') {
    list = list.filter(item => item.teamId !== 'default')
  }

  // 根据搜索条件筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    list = list.filter(item => {
      return item.templateName.toLowerCase().includes(query) ||
        (item.teamName && item.teamName.toLowerCase().includes(query)) ||
        (item.description && item.description.toLowerCase().includes(query))
    })
  }

  return list
})

// 获取模板列表
const fetchTemplateList = async () => {
  loading.value = true
  try {
    const res = await getTemplateList()
    if (res.code === 200 && res.data) {
      templateList.value = res.data
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  // 这里不需要做任何事情，因为我们使用的是计算属性
}

// 处理创建
const handleCreate = () => {
  dialogType.value = 'create'
  currentTemplate.value = {
    templateName: '',
    teamId: '',
    description: ''
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  dialogType.value = 'edit'
  currentTemplate.value = {
    id: row.id,
    templateName: row.templateName,
    teamId: row.teamId,
    description: row.description
  }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除模板 "${row.templateName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteTemplate(row.id)
      ElMessage.success('删除成功')
      fetchTemplateList()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 跳转到详情页面
const goToDetail = (row) => {
  router.push({
    name: 'TemplateDetail',
    params: { id: row.id }
  })
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (dialogType.value === 'create') {
      await createTemplate(formData)
      ElMessage.success('创建成功')
    } else {
      formData.id = currentTemplate.value.id
      await updateTemplate(formData)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    fetchTemplateList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 生命周期钩子
onMounted(() => {
  fetchTemplateList()
})
</script>

<style scoped>
.template-management-container {
  padding: 20px;
  position: relative;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.add-button {
  position: fixed;
  right: 50px;
  bottom: 50px;
  z-index: 10;
}
</style>
