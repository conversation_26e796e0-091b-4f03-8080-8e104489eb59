<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    class="template-form"
  >
    <el-form-item label="模板名称" prop="templateName">
      <el-input v-model="form.templateName" placeholder="请输入模板名称" />
    </el-form-item>
    
    <el-form-item label="管理团队" prop="teamId">
      <el-select
        v-model="form.teamId"
        placeholder="请选择团队"
        class="w-100"
        clearable
      >
        <el-option
          v-for="item in teamOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="模板描述" prop="description">
      <el-input
        v-model="form.description"
        type="textarea"
        placeholder="请输入模板描述"
        :rows="4"
      />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps, onMounted } from 'vue'
import { getTeamList } from '@/api/templateManagement'

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({
      templateName: '',
      teamId: '',
      description: ''
    })
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref(null)
const teamOptions = ref([])

const form = reactive({
  templateName: props.initialData.templateName,
  teamId: props.initialData.teamId,
  description: props.initialData.description
})

const rules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  teamId: [
    { required: false, message: '请选择管理团队', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 获取团队列表
const fetchTeamList = async () => {
  try {
    const res = await getTeamList()
    if (res.code === 200 && res.data) {
      teamOptions.value = res.data
    }
  } catch (error) {
    console.error('获取团队列表失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
    }
  })
}

// 取消
const cancel = () => {
  emit('cancel')
}

onMounted(() => {
  fetchTeamList()
})
</script>

<style scoped>
.template-form {
  max-width: 600px;
}
.w-100 {
  width: 100%;
}
</style> 