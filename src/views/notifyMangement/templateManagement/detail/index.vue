<template>
  <div class="template-detail-container">
    <div class="template-layout">
      <!-- 左侧：通知应用列表 -->
      <div class="sidebar">
        <h3 class="sidebar-title">通知应用</h3>
        <el-menu
          :default-active="activeTab"
          class="sidebar-menu"
          @select="handleSelect"
        >
          <el-menu-item index="notify-app">
            <el-icon><bell /></el-icon>
            <span>通知应用</span>
          </el-menu-item>
          <el-menu-item index="email-app">
            <el-icon><message /></el-icon>
            <span>邮件应用</span>
          </el-menu-item>
          <el-menu-item index="kingsoft-app">
            <el-icon><platform /></el-icon>
            <span>金山协作应用</span>
          </el-menu-item>
          <el-menu-item index="kingsoft-robot">
            <el-icon><chat-dot-round /></el-icon>
            <span>金山协作机器人</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 中间：模板内容 -->
      <div class="content">
        <div class="content-header">
          <el-button @click="goBack">返回</el-button>
          <div class="header-title">
            <h2>{{ template.templateName }}</h2>
            <el-tag v-if="template.teamName">{{ template.teamName }}</el-tag>
          </div>
        </div>
        
        <div class="content-body">
          <template-content :template-data="template" :active-tab="activeTab" @show-preview="handlePreview" />
        </div>
      </div>

      <!-- 右侧：预览与文档 -->
      <div class="document-section">
        <el-tabs v-model="documentTab" class="document-tabs">
          <el-tab-pane name="preview">
            <template #label>
              <div class="tab-label">
                <el-icon><View /></el-icon>
                <span>预览</span>
              </div>
            </template>
            <div class="preview-section">
              <div class="preview-header">
                <span>预览数据源</span>
                <el-select v-model="previewDataSource" placeholder="选择数据源" class="data-source-select">
                  <el-option label="BBDDE6 TestAlert / Grafana" value="test1" />
                </el-select>
              </div>
              <div class="preview-content">
                <div class="alert-card">
                  <div class="alert-icon">
                    <el-avatar size="medium" icon="Bell" />
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">[处理中] #BBDDE6 TestAlert / Grafana</div>
                    <div class="alert-info">
                      <div>description :22</div>
                      <div>resource : Grafana</div>
                      <div>alertname : TestAlert</div>
                      <div>check : TestAlert</div>
                      <div>instance : Grafana</div>
                      <div>summary : 111</div>
                      <div>test1 : 1</div>
                      <div>test2 : 2</div>
                      <div>test3 : 3</div>
                      <div>valueString : [ metric='foo' labels=(instance=bar) value=10 ]</div>
                      <div>团队空间: 测试告警服务</div>
                      <div>严重程度: Warning</div>
                      <div>处理人: @zhangyupeng2</div>
                    </div>
                    <div class="alert-actions">
                      <el-button size="small">详情</el-button>
                      <el-button size="small" type="primary">认领</el-button>
                      <el-button size="small" type="danger">关闭</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="document">
            <template #label>
              <div class="tab-label">
                <el-icon><document /></el-icon>
                <span>文档</span>
              </div>
            </template>
            <markdown-doc :doc-name="activeTab" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Bell, Platform, ChatDotRound, View, Document, Message } from '@element-plus/icons-vue'
import { getTemplateDetail } from '@/api/templateManagement'
import MarkdownDoc from './components/MarkdownDoc.vue'
import TemplateContent from './components/TemplateContent.vue'

defineOptions({
  name: 'TemplateDetail'
})

const route = useRoute()
const router = useRouter()
const templateId = route.params.id

const template = ref({
  id: null,
  templateName: '通知模板',
  teamId: '',
  teamName: '测试团队',
  description: '',
  updateTime: ''
})

const activeTab = ref('notify-app')
const documentTab = ref('preview')
const previewDataSource = ref('test1')

// 加载模板详情数据
const fetchTemplateDetail = async () => {
  try {
    const res = await getTemplateDetail(templateId)
    if (res.code === 200 && res.data) {
      template.value = res.data
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    ElMessage.error('获取模板详情失败')
    // 使用模拟数据
    template.value = {
      id: templateId,
      templateName: '通知模板',
      teamId: 'team_1',
      teamName: '测试团队',
      description: '用于测试的通知模板',
      updateTime: new Date().toLocaleString()
    }
  }
}

// 处理侧边栏选项选择
const handleSelect = (key) => {
  activeTab.value = key
}

// 返回模板列表页面
const goBack = () => {
  router.back()
}

// 处理预览效果
const handlePreview = (previewData) => {
  // 实现预览效果的逻辑
  console.log('预览效果:', previewData)
  // 替换预览内容中的变量
  const previewContent = document.createElement('div')
  
  // 根据不同的应用类型和通知级别设置预览样式
  if (activeTab.value === 'notify-app') {
    let content = previewData.content
      .replace(/{{alertname}}/g, 'TestAlert')
      .replace(/{{time}}/g, new Date().toLocaleString())
      .replace(/{{severity}}/g, previewData.level === 'urgent' ? '紧急' : previewData.level === 'important' ? '重要' : '普通')
      .replace(/{{description}}/g, '测试告警描述')
      .replace(/{{resource}}/g, 'Grafana')
      .replace(/{{instance}}/g, 'server-prod-01')
      .replace(/{{summary}}/g, '这是一个测试告警')
      .replace(/{{dashboard_url}}/g, '#')
    
    const title = previewData.title
      .replace(/{{alertname}}/g, 'TestAlert')
    
    previewContent.innerHTML = `
      <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px;">${title}</div>
      <div style="white-space: pre-line;">${content}</div>
    `
  } else if (activeTab.value === 'email-app') {
    // 邮件应用的预览处理
    let emailContent = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
      <div style="max-width: 100%; overflow: auto; border: 1px solid #e6e6e6; padding: 10px; border-radius: 4px;">
        <div style="width:100%;height:0.375rem;margin-bottom:1rem" class="bg-Warning"></div>
        <div style="display:flex;align-items:center;margin-bottom:1rem">
          <div style="display:flex;"><span style="margin-top:0.5rem;font-size:1.25rem;line-height:1.75rem;font-weight:600">您有故障待处理</span></div>
        </div>
        <div style="padding:1rem;margin-top:1rem;border-radius:0.5rem;background-color:#f5f7fa;border:1px solid #e6e6e6;">
          <div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">故障标题</div>
              <div>测试告警 - ${previewData.title || 'TestAlert'}</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">严重程度</div>
              <div style="color:#ffaa44">Warning</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">协作空间</div>
              <div>测试告警服务</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">触发时间</div>
              <div>${new Date().toLocaleString()}</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">处理人员</div>
              <div>@zhangyupeng2</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">处理进度</div>
              <div>处理中</div>
            </div>
            <div style="display:flex;margin-bottom:0.5rem;">
              <div style="font-weight:500;width:100px;min-width:100px;margin-right:1rem;">故障描述</div>
              <div>${previewData.content || '这是一个测试告警描述'}</div>
            </div>
          </div>
          <div style="display:flex;margin-top:1rem">
            <button style="margin-right:1rem;padding:0.5rem 2rem;background-color:rgb(108,83,177);border-radius:0.25rem;font-size:1rem;color:white;font-weight:600;border:none;">
              立即认领
            </button>
            <button style="padding:0.5rem 2rem;border:1px solid #e6e6e6;border-radius:0.25rem;font-size:1rem;font-weight:600;background-color:white;">
              查看详情
            </button>
          </div>
        </div>
        <div style="display:flex;justify-content:flex-end;margin-top:1rem">
          <div style="font-size:0.875rem;font-weight:500">版权所有 © SeasunGames</div>
        </div>
      </div>
    `;
    
    previewContent.innerHTML = emailContent;
  }
  
  // 更新预览区域
  const previewElement = document.querySelector('.alert-info')
  if (previewElement) {
    previewElement.innerHTML = ''
    previewElement.appendChild(previewContent)
  }
  
  // 切换到预览选项卡
  documentTab.value = 'preview'
}

onMounted(() => {
  fetchTemplateDetail()
})
</script>

<style scoped>
.template-detail-container {
  height: calc(100vh - 84px);
  overflow: hidden;
}

.template-layout {
  display: flex;
  height: 100%;
}

/* 左侧侧边栏 */
.sidebar {
  width: 180px;
  background-color: #f5f7fa;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
}

.sidebar-title {
  padding: 0 20px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}

/* 中间内容区 */
.content {
  flex: 6; /* 占比为6 */
  overflow: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e6e6;
}

.content-header {
  display: flex;
  align-items: center;
}

.header-title {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.header-title h2 {
  margin-right: 10px;
  margin-bottom: 0;
}

.content-body {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  overflow: auto;
}

/* 右侧文档区 */
.document-section {
  flex: 4; /* 占比为4 */
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-tabs {
  height: 100%;
  display: flex;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
  padding: 0;
}

:deep(.el-tab-pane) {
  height: 100%;
}

.preview-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 16px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e6e6e6;
}

.data-source-select {
  width: 220px;
}

.preview-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.alert-card {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
}

.alert-icon {
  margin-right: 16px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 12px;
}

.alert-info {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.alert-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.app-content {
  padding: 20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 5px;
}

.tab-label .el-icon {
  margin-right: 4px;
}
</style> 