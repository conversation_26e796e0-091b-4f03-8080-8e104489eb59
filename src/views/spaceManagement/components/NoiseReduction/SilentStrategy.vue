<template>
  <div class="silent-strategy-container">
    <div class="section-header">
      <div class="section-title">
        <span class="title-icon">
          <el-icon><InfoFilled /></el-icon>
        </span>
        静默策略
        <el-tooltip
          content="静默策略可以设置特定条件下的告警不触发通知"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </div>

    <div class="section-content">
      <div class="empty-state" v-if="strategies.length === 0">
        <div class="empty-content">
          <el-icon class="empty-icon"><DocumentAdd /></el-icon>
          <div class="empty-text">暂无静默策略</div>
          <el-button type="primary" @click="addStrategy">新增一条策略</el-button>
        </div>
      </div>

      <div class="strategy-list" v-else>
        <div v-for="(strategy, index) in strategies" :key="index" class="strategy-item">
          <div class="strategy-header">
            <div class="strategy-title">{{ strategy.name }}</div>
            <div class="strategy-actions">
              <el-button type="text" @click="editStrategy(strategy)">编辑</el-button>
              <el-button type="text" @click="deleteStrategy(strategy)">删除</el-button>
            </div>
          </div>
          <div class="strategy-content">
            <div class="strategy-description">{{ strategy.description }}</div>
            <div class="strategy-conditions">
              <div class="condition-item" v-for="(condition, idx) in strategy.conditions" :key="idx">
                <el-tag size="small">{{ condition }}</el-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="add-strategy">
          <el-button type="primary" plain @click="addStrategy">
            <el-icon><Plus /></el-icon>新增一条策略
          </el-button>
        </div>
      </div>

      <!-- 策略表单对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑静默策略' : '新增静默策略'"
        width="600px"
        destroy-on-close
      >
        <el-form :model="strategyForm" label-width="100px">
          <el-form-item label="策略名称" required>
            <el-input v-model="strategyForm.name" placeholder="请输入策略名称"></el-input>
          </el-form-item>
          <el-form-item label="策略描述">
            <el-input
              v-model="strategyForm.description"
              type="textarea"
              placeholder="请输入策略描述"
              :rows="3"
            ></el-input>
          </el-form-item>
          <el-form-item label="匹配条件" required>
            <div class="condition-form">
              <div v-for="(condition, index) in strategyForm.conditions" :key="index" class="condition-form-item">
                <el-input v-model="strategyForm.conditions[index]" placeholder="例如: severity=critical"></el-input>
                <el-button type="danger" circle @click="removeCondition(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <el-button type="primary" plain @click="addCondition">
                <el-icon><Plus /></el-icon>添加条件
              </el-button>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveStrategy">保 存</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { InfoFilled, QuestionFilled, DocumentAdd, Plus, Delete } from '@element-plus/icons-vue'
import {
  fetchSilentStrategies,
  createSilentStrategyApi,
  updateSilentStrategyApi,
  deleteSilentStrategyApi
} from '@/api/noiseReduction'

const props = defineProps({
  spaceId: {
    type: [Number, String],
    required: true
  }
})

// 静默策略列表
const strategies = ref([])

// 对话框可见性
const dialogVisible = ref(false)

// 是否为编辑模式
const isEdit = ref(false)

// 当前编辑的策略ID
const currentStrategyId = ref(null)

// 策略表单
const strategyForm = reactive({
  name: '',
  description: '',
  conditions: ['']
})

// 加载策略列表
const loadStrategies = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const response = await fetchSilentStrategies(props.spaceId)
    if (response.code === 0 && response.data) {
      strategies.value = response.data.strategies || []
    }
  } catch (error) {
    console.error('加载静默策略失败:', error)
    ElMessage.error('加载策略失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 添加策略
const addStrategy = () => {
  isEdit.value = false
  currentStrategyId.value = null
  strategyForm.name = ''
  strategyForm.description = ''
  strategyForm.conditions = ['']
  dialogVisible.value = true
}

// 编辑策略
const editStrategy = (strategy) => {
  isEdit.value = true
  currentStrategyId.value = strategy.id
  strategyForm.name = strategy.name
  strategyForm.description = strategy.description
  strategyForm.conditions = [...strategy.conditions]
  dialogVisible.value = true
}

// 删除策略
const deleteStrategy = (strategy) => {
  ElMessageBox.confirm('确定要删除该静默策略吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const response = await deleteSilentStrategyApi(props.spaceId, strategy.id)
      if (response.code === 0 && response.data.success) {
        const index = strategies.value.findIndex(s => s.id === strategy.id)
        if (index !== -1) {
          strategies.value.splice(index, 1)
        }
        ElMessage.success('删除成功')
      } else {
        ElMessage.error('删除失败，请稍后重试')
      }
    } catch (error) {
      console.error('删除静默策略失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    } finally {
      loading.close()
    }
  }).catch(() => {})
}

// 添加条件
const addCondition = () => {
  strategyForm.conditions.push('')
}

// 移除条件
const removeCondition = (index) => {
  strategyForm.conditions.splice(index, 1)
  if (strategyForm.conditions.length === 0) {
    strategyForm.conditions.push('')
  }
}

// 保存策略
const saveStrategy = async () => {
  if (!strategyForm.name.trim()) {
    ElMessage.warning('请输入策略名称')
    return
  }

  if (strategyForm.conditions.some(c => !c.trim())) {
    ElMessage.warning('请填写所有条件或删除空条件')
    return
  }

  const loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const strategyData = {
      name: strategyForm.name,
      description: strategyForm.description,
      conditions: strategyForm.conditions.filter(c => c.trim())
    }

    let response

    if (isEdit.value && currentStrategyId.value) {
      response = await updateSilentStrategyApi(props.spaceId, currentStrategyId.value, strategyData)
      if (response.code === 0 && response.data.success) {
        // 更新本地数据
        const index = strategies.value.findIndex(s => s.id === currentStrategyId.value)
        if (index !== -1) {
          strategies.value[index] = {
            ...strategies.value[index],
            ...strategyData
          }
        }
        ElMessage.success('策略更新成功')
      } else {
        ElMessage.error('更新失败，请稍后重试')
      }
    } else {
      response = await createSilentStrategyApi(props.spaceId, strategyData)
      if (response.code === 0 && response.data) {
        // 添加到本地数据
        strategies.value.push(response.data)
        ElMessage.success('策略添加成功')
      } else {
        ElMessage.error('添加失败，请稍后重试')
      }
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('保存静默策略失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    loading.close()
  }
}

// 组件挂载时加载策略列表
onMounted(() => {
  loadStrategies()
})
</script>

<script>
export default {
  name: 'SilentStrategy'
}
</script>

<style scoped>
.silent-strategy-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.help-icon {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
}

.section-content {
  padding: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  color: #909399;
  margin-bottom: 16px;
}

.strategy-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.strategy-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.strategy-title {
  font-weight: 500;
  color: #303133;
}

.strategy-actions {
  display: flex;
  gap: 8px;
}

.strategy-content {
  padding: 16px;
}

.strategy-description {
  color: #606266;
  margin-bottom: 12px;
}

.strategy-conditions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.add-strategy {
  margin-top: 16px;
}

.condition-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.condition-form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
