<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>ZeroDuty 值班管理系统</h1>
        <p>请使用您的账户登录</p>
      </div>
      
      <div class="login-content">
        <div v-if="loginStatus === 'loading'" class="loading-state">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>正在验证身份...</p>
        </div>
        
        <div v-else-if="loginStatus === 'error'" class="error-state">
          <el-icon class="error-icon"><Warning /></el-icon>
          <p>{{ errorMessage }}</p>
          <el-button type="primary" @click="retryLogin">重试登录</el-button>
        </div>
        
        <div v-else class="success-state">
          <el-icon class="success-icon"><CircleCheck /></el-icon>
          <p>登录成功，正在跳转...</p>
        </div>
      </div>
      
      <div class="login-footer">
        <p>© 2024 ZeroDuty Team. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Loading, Warning, CircleCheck } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/modules/authStore'
import { handleLoginRedirect } from '@/router/guards/authGuard'

defineOptions({
  name: 'LoginPage'
})

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loginStatus = ref('loading') // loading, success, error
const errorMessage = ref('')

/**
 * 执行登录
 */
const performLogin = async () => {
  try {
    loginStatus.value = 'loading'
    
    const result = await authStore.loginWithCookie()
    
    if (result.success) {
      loginStatus.value = 'success'
      
      // 延迟跳转，让用户看到成功状态
      setTimeout(() => {
        handleLoginRedirect(route, router)
      }, 1000)
    } else {
      loginStatus.value = 'error'
      errorMessage.value = result.error || '登录失败，请重试'
    }
  } catch (error) {
    loginStatus.value = 'error'
    errorMessage.value = error.message || '登录过程中发生错误'
  }
}

/**
 * 重试登录
 */
const retryLogin = () => {
  performLogin()
}

// 组件挂载时自动尝试登录
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isLoggedIn && authStore.isTokenValid) {
    loginStatus.value = 'success'
    setTimeout(() => {
      handleLoginRedirect(route, router)
    }, 500)
    return
  }
  
  // 否则尝试Cookie登录
  performLogin()
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-header h1 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #606266;
  margin-bottom: 32px;
  font-size: 14px;
}

.login-content {
  margin-bottom: 32px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-state,
.error-state,
.success-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  font-size: 48px;
  color: #409eff;
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-state p,
.error-state p,
.success-state p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.login-footer p {
  color: #909399;
  font-size: 12px;
  margin: 0;
}

.el-button {
  margin-top: 8px;
}
</style>
