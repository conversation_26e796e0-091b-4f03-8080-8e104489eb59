<template>
  <div class="integration-management-container">
    <!-- 左侧集成类型导航 -->
    <div class="left-panel">
      <div class="left-header">
        <h3 class="panel-title">集成类型</h3>
        <!-- <el-button type="primary" size="small" @click="addNewIntegration()">新增集成</el-button> -->
      </div>
      <div class="integration-types" v-loading="typeLoading">
        <div
          v-for="item in integrationTypes"
          :key="item.id"
          class="integration-type-item"
          :class="{ active: activeType === item.id }"
          @click="selectIntegrationType(item)">
          <div class="type-item-content">
            <div class="icon-container">
              <img :src="getTypeLargeIconSrc(item.name)" class="integration-icon" :class="getIconClass(item.name)" />
            </div>
            <div class="integration-type-name">{{ item.name }}</div>
          </div>
          <div class="integration-type-actions">
            <el-button
              type="success"
              size="small"
              circle
              icon="Plus"
              @click.stop="addNewIntegration(item)"
              title="添加该类型集成">
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="right-panel">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          v-model="searchTypeModel"
          placeholder="搜索集成类型"
          class="search-input">
        </el-input>
        <el-input
          v-model="searchNameModel"
          placeholder="搜索集成名称"
          class="search-input">
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态" class="filter-select">
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" value="enabled"></el-option>
          <el-option label="禁用" value="disabled"></el-option>
        </el-select>
        <el-select v-model="typeFilter" placeholder="类型" class="filter-select">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>

      <!-- 内容主体区域 -->
      <div class="content-area">
        <!-- 集成列表 -->
        <el-table
          :data="filteredIntegrations"
          style="width: 100%"
          class="integration-table"
          v-loading="tableLoading"
          element-loading-text="加载集成数据中">
          <el-table-column prop="name" label="集成" min-width="180">
            <template #default="scope">
              <div class="integration-cell">
                <div class="integration-icon-small">
                  <img :src="getTypeIconSrc(scope.row.type)" class="cell-icon" />
                </div>
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" min-width="180"></el-table-column>
          <el-table-column prop="lastEventTime" label="最新事件时间" min-width="180"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-switch
                :model-value="scope.row.status"
                @change="(value) => toggleStatus(scope.row, value)"
                :active-value="'enabled'"
                :inactive-value="'disabled'"
                :loading="scope.row.statusLoading">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button type="primary" :icon="Edit" circle @click="editIntegration(scope.row)"></el-button>
              <el-button type="danger" :icon="Delete" circle @click="deleteIntegration(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="total-info">总计 {{ totalItems }} 项</div>
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            layout="prev, pager, next"
            :total="totalItems">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Edit, Delete } from '@element-plus/icons-vue';
import { getIntegrationTypes, getIntegrations, getIntegrationById, toggleIntegrationStatus, deleteIntegrations } from '@/api/integrationAdapter';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

// 导入图标 - small版本
import prometheusIcon from './logo/small/prometheus.svg';
import aliyunIcon from './logo/small/aliyun_cm.svg';
import grafanaIcon from './logo/small/grafana.svg';
import zabbixIcon from './logo/small/zabbix.svg';
import emailIcon from './logo/small/email-dark.svg';
import customIcon from './logo/small/custom-dark.svg';

// 导入图标 - large版本
import prometheusIconLarge from './logo/large/prometheus.png';
import aliyunIconLarge from './logo/small/aliyun_cm.svg';
import grafanaIconLarge from './logo/large/grafana.svg';
import zabbixIconLarge from './logo/large/zabbix.svg';
import emailIconLarge from './logo/large/email-dark.svg';
import customIconLarge from './logo/large/custom-dark.svg';

// 组件名称定义
defineOptions({
  name: 'IntegrationManagement'
});

// 获取路由实例
const router = useRouter();

// 活动类型
const activeType = ref(null);

// 搜索和筛选
const searchTypeModel = ref('');
const searchNameModel = ref('');
const statusFilter = ref('');
const typeFilter = ref('');

// 集成类型选项
const typeOptions = ref([]);

// 集成类型图标
const integrationTypes = ref([]);

// 集成数据
const integrations = ref([]);

// 加载状态
const typeLoading = ref(false);
const tableLoading = ref(false);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 过滤集成
const filteredIntegrations = computed(() => {
  return integrations.value.filter(item => {
    const matchType = !searchTypeModel.value || item.type.toLowerCase().includes(searchTypeModel.value.toLowerCase());
    const matchName = !searchNameModel.value || item.name.toLowerCase().includes(searchNameModel.value.toLowerCase());
    const matchStatus = !statusFilter.value || item.status === statusFilter.value;
    const matchTypeFilter = !typeFilter.value || item.type === typeFilter.value; // 使用精确匹配类型全名
    const matchActiveType = !activeType.value || item.typeId === activeType.value;
    return matchType && matchName && matchStatus && matchTypeFilter && matchActiveType;
  });
});

// 获取类型图标路径 - small版本
const getTypeIconSrc = (type) => {
  const typeIconMap = {
    'Prometheus': prometheusIcon,
    '阿里云监控 CM 事件': aliyunIcon,
    'Grafana': grafanaIcon,
    'Zabbix': zabbixIcon,
    '邮件 Email': emailIcon,
    '标准告警事件': customIcon
  };
  return typeIconMap[type] || customIcon;
};

// 获取类型图标路径 - large版本
const getTypeLargeIconSrc = (type) => {
  const typeIconMap = {
    'Prometheus': prometheusIconLarge,
    '阿里云监控 CM 事件': aliyunIconLarge,
    'Grafana': grafanaIconLarge,
    'Zabbix': zabbixIconLarge,
    '邮件 Email': emailIconLarge,
    '标准告警事件': customIconLarge
  };
  return typeIconMap[type] || customIconLarge;
};

// 为SVG图标添加样式类，使邮件和标准告警填充黑色
const getIconClass = (type) => {
  if (type === '邮件 Email' || type === '标准告警事件') {
    return 'dark-fill-icon';
  }
  return '';
};

// 选择集成类型
const selectIntegrationType = (item) => {
  activeType.value = activeType.value === item.id ? null : item.id;

  // 重新加载符合当前筛选条件的数据
  loadIntegrations();
};

// 切换状态
const toggleStatus = async (row, newValue) => {
  // 设置加载状态
  row.statusLoading = true;

  try {
    console.log('🔄 页面切换状态 - 行数据:', row); // 调试信息
    console.log('🔄 使用的ID:', row.id, '类型:', typeof row.id); // 调试信息
    console.log('🔄 新状态值:', newValue); // 调试信息

    await toggleIntegrationStatus(row.id, newValue, row);

    // 只有成功时才更新状态
    row.status = newValue;
    ElMessage.success(`已${newValue === 'enabled' ? '启用' : '禁用'}集成：${row.name}`);

    // 成功后刷新页面数据
    await loadIntegrations();
  } catch (error) {
    console.error('切换集成状态失败:', error);
    // 失败时不更新状态，保持原状态
    ElMessage.error(error.message || '切换状态失败');
  } finally {
    // 清除加载状态
    row.statusLoading = false;
  }
};

// 添加新集成
const addNewIntegration = (typeItem = null) => {
  // 构建导航路径
  let routePath = '/oncall/integration/add';

  // 如果有指定类型，则附加到路径
  if (typeItem) {
    routePath += `/${typeItem.name.toLowerCase()}`;
    // 保存最后查看的类型ID到localStorage，用于mock数据
    localStorage.setItem('lastViewedTypeId', typeItem.id.toString());
  }

  // 导航到添加页面
  router.push(routePath);
};

// 编辑集成
const editIntegration = async (row) => {
  try {
    console.log('🔍 编辑集成 - 行数据:', row); // 调试信息

    // 保存最后查看的类型ID到localStorage，用于mock数据
    localStorage.setItem('lastViewedTypeId', row.typeId.toString());

    // 先请求详情接口获取完整数据
    const detailResponse = await getIntegrationById(row.id);
    console.log('📋 获取详情响应:', detailResponse); // 调试信息

    // 处理API响应数据结构
    const detailData = detailResponse.data || detailResponse;
    console.log('📋 详情数据:', detailData); // 调试信息

    // 保存完整的集成详情数据到localStorage，供详情页面使用
    localStorage.setItem('currentIntegrationData', JSON.stringify({
      id: detailData.id,
      name: detailData.name,
      type: detailData.type,
      typeId: row.typeId, // 使用列表中的typeId
      status: detailData.status ? 'enabled' : 'disabled',
      lastEventTime: detailData.lastEventTime || row.lastEventTime,
      webhook: detailData.webhook || '',
      kind: detailData.kind || 'public',
      // 添加其他可能的详情字段
      trans: detailData.trans,
      dataSource: detailData.dataSource,
      createdAt: detailData.createdAt,
      updatedAt: detailData.updatedAt
    }));

    // 构建编辑路径：/oncall/integration/detail/{type}/{id}
    const routePath = `/oncall/integration/detail/${row.type.toLowerCase()}/${row.id}`;

    // 导航到详情/编辑页面
    router.push(routePath);
  } catch (error) {
    console.error('获取集成详情失败:', error);
    ElMessage.error(error.message || '获取集成详情失败');
  }
};

// 删除集成
const deleteIntegration = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除集成 "${row.name}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    await deleteIntegrations([row.id]);
    ElMessage.success('删除成功');
    // 重新加载数据
    await loadIntegrations();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除集成失败:', error);
      ElMessage.error(error.message || '删除失败');
    }
  }
};

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadIntegrations();
};

// 加载集成类型
const loadIntegrationTypes = async () => {
  typeLoading.value = true;
  try {
    const data = await getIntegrationTypes();
    integrationTypes.value = data;

    // 从集成类型中提取类型选项
    typeOptions.value = data.map(item => ({
      value: item.name, // 使用类型全名作为value
      label: item.name
    }));
  } catch (error) {
    console.error('加载集成类型失败:', error);
    ElMessage.error(error.message || '加载集成类型失败');
  } finally {
    typeLoading.value = false;
  }
};

// 加载集成数据
const loadIntegrations = async () => {
  tableLoading.value = true;
  try {
    const { data, total } = await getIntegrations({
      page: currentPage.value,
      pageSize: pageSize.value,
      typeId: activeType.value,
      status: statusFilter.value,
      typeKeyword: typeFilter.value || searchTypeModel.value, // 优先使用typeFilter，如果为空则使用searchTypeModel
      nameKeyword: searchNameModel.value
    });

    integrations.value = data;
    totalItems.value = total;
  } catch (error) {
    console.error('加载集成数据失败:', error);
    ElMessage.error(error.message || '加载集成数据失败');
  } finally {
    tableLoading.value = false;
  }
};

// 加载数据
onMounted(async () => {
  await loadIntegrationTypes();
  await loadIntegrations();
});

// 监听过滤器变化，自动刷新数据
watch([searchNameModel, searchTypeModel, statusFilter, typeFilter], () => {
  loadIntegrations();
}, { debounce: 300 });
</script>

<style lang="scss" scoped>
.integration-management-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #fff;
  color: #333;
  overflow: hidden;
}

.left-panel {
  width: 480px;
  max-width: 22%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-shrink: 0;
  padding: 0;
  background-color: #fff;
  color: #333;
  border-right: 1px solid #eaeaea;
}

.left-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f9f9;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow-y: auto;
  min-width: 0; /* 防止子元素溢出 */
}

.search-area {
  display: flex;
  gap: 10px;
  margin: 10px;
  flex-wrap: wrap;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 10px 10px;
  min-height: 0; /* 避免内容溢出 */
}

.integration-types {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 10px;
  overflow-y: auto;
  justify-content: flex-start;
  align-content: flex-start;
}

.integration-type-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: #f5f7fa;
  height: 105px;
  position: relative;
  border: 1px solid #eaeaea;

  &:hover, &.active {
    background-color: #ecf5ff;
    border-color: #d9ecff;
  }

  &.active {
    border-left: 3px solid #409eff;
  }
}

.type-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.icon-container {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  flex-shrink: 0;
  overflow: hidden;
}

.integration-icon {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.integration-type-name {
  font-size: 14px;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.integration-type-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.integration-type-item:hover .integration-type-actions {
  opacity: 1;
}

.integration-table {
  margin-bottom: 10px;
  flex: 1;
}

.integration-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.integration-icon-small {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cell-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.total-info {
  color: #606266;
}

.status-text {
  margin-left: 10px;
  color: #666;
}

/* 修改滚动条样式 */
:deep(::-webkit-scrollbar) {
  width: 4px;
  height: 4px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

:deep(::-webkit-scrollbar-track) {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

// 添加黑色填充样式
.dark-fill-icon {
  filter: brightness(0);
}
</style>