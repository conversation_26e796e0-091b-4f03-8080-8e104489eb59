<template>
  <div class="integration-form">
    <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
      <el-form-item label="集成名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入集成名称"></el-input>
      </el-form-item>
      <el-form-item label="集成类型" prop="typeId">
        <!-- 编辑模式下显示只读文本 -->
        <template v-if="mode === 'edit'">
          <div class="type-display">
            <div class="option-icon">
              <img :src="getTypeIconSrc(formData.type)" class="icon-img" />
            </div>
            <span>{{ formData.type }}</span>
          </div>
        </template>
        <!-- 新增模式下显示下拉框 -->
        <template v-else>
          <el-select
            v-model="formData.typeId"
            placeholder="请选择集成类型"
            :disabled="isTypeFixed"
            @change="handleTypeChange">
            <el-option
              v-for="item in integrationTypes"
              :key="item.id"
              :label="item.name"
              :value="item.id">
              <div class="type-option">
                <div class="option-icon">
                  <img :src="getTypeIconSrc(item.name)" class="icon-img" />
                </div>
                <span>{{ item.name }}</span>
              </div>
            </el-option>
          </el-select>
        </template>
      </el-form-item>

      <!-- 推送地址字段，所有集成类型都显示 -->
      <el-form-item label="推送地址">
        <el-input v-model="formData.webhook" readonly placeholder="系统自动生成推送地址">
          <template #append>
            <el-button type="primary" @click="copyPushUrl">复制</el-button>
          </template>
        </el-input>
        <div class="form-tip">
          通过推送地址将{{ formData.type || '集成' }}告警事件推送到zeroduty。
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="'enabled'"
          :inactive-value="'disabled'">
        </el-switch>
        <span class="status-text">{{ formData.status === 'enabled' ? '启用' : '禁用' }}</span>
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button @click="cancelForm">取消</el-button>
      <el-button type="primary" @click="submitForm">{{ mode === 'add' ? '创建' : '保存' }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { generatePushUrl } from '@/utils/webhook';

// 导入图标
import prometheusIcon from '../logo/small/prometheus.svg';
import aliyunIcon from '../logo/small/aliyun_cm.svg';
import grafanaIcon from '../logo/small/grafana.svg';
import zabbixIcon from '../logo/small/zabbix.svg';
import emailIcon from '../logo/small/email-dark.svg';
import customIcon from '../logo/small/custom-dark.svg';

const props = defineProps({
  mode: {
    type: String,
    default: 'add', // 'add' 或 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  formModel: {
    type: Object,
    default: () => ({
      id: null,
      name: '',
      typeId: null,
      type: '',
      status: 'enabled',
      webhook: ''
    })
  },
  integrationTypes: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['cancel', 'submit']);

// 表单数据
const formData = reactive({...props.formModel});

// 表单引用
const formRef = ref(null);

// 判断类型是否已经固定（从路由参数或其他方式预设）
const isTypeFixed = computed(() => {
  return props.mode === 'add' && formData.typeId !== null && formData.type !== '';
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入集成名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  typeId: [
    { required: true, message: '请选择集成类型', trigger: 'change' }
  ]
};

// 当props中的formModel变化时，更新本地formData
watch(() => props.formModel, (newVal) => {
  Object.assign(formData, newVal);
  generatePushAddress();
}, { deep: true });

// 处理类型变更
const handleTypeChange = (typeId) => {
  // 找到对应的类型信息
  const selectedType = props.integrationTypes.find(item => item.id === typeId);
  if (selectedType) {
    formData.type = selectedType.name;
  }
};

// 获取类型图标路径
const getTypeIconSrc = (type) => {
  const typeIconMap = {
    'Prometheus': prometheusIcon,
    '阿里云监控 CM 事件': aliyunIcon,
    'Grafana': grafanaIcon,
    'Zabbix': zabbixIcon,
    '邮件 Email': emailIcon,
    '标准告警事件': customIcon
  };
  return typeIconMap[type] || customIcon;
};

// 取消表单
const cancelForm = () => {
  emit('cancel');
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit('submit', {...formData});
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 复制推送地址
const copyPushUrl = () => {
  if (formData.webhook) {
    navigator.clipboard.writeText(formData.webhook)
      .then(() => {
        ElMessage.success('推送地址已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制');
      });
  }
};

// 自动生成推送地址（如果为空）
const generatePushAddress = () => {
  if (!formData.webhook && formData.typeId && formData.type) {
    // 在添加模式下，如果有真实ID，则使用真实ID生成推送地址
    const useRealId = props.mode === 'add' && formData.id && formData.id !== 'new';
    formData.webhook = generatePushUrl(formData.type, formData.id || 'new', useRealId);
  }
};

// 当类型变更时，重新生成推送地址
watch(() => [formData.typeId, formData.type], () => {
  if (formData.typeId && formData.type) {
    // 在添加模式下，如果有真实ID，则使用真实ID生成推送地址
    const useRealId = props.mode === 'add' && formData.id && formData.id !== 'new';
    formData.webhook = generatePushUrl(formData.type, formData.id || 'new', useRealId);
  }
});

// 当ID变更时，重新生成推送地址（用于添加模式下获取到UUID后更新推送地址）
watch(() => formData.id, () => {
  if (props.mode === 'add' && formData.id && formData.typeId && formData.type) {
    const useRealId = formData.id !== 'new';
    formData.webhook = generatePushUrl(formData.type, formData.id, useRealId);
  }
});

// 组件挂载时，生成推送地址（如果需要）
onMounted(() => {
  generatePushAddress();
});
</script>

<style lang="scss" scoped>
.integration-form {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 10px;
}

.type-display {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 32px;
  padding: 0 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.icon-img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.status-text {
  margin-left: 10px;
  color: #666;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  margin-bottom: 0;
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 15px 0;
  z-index: 10;
  border-top: 1px solid #f0f0f0;

  .el-button {
    min-width: 100px;
    padding: 10px 25px;
    font-size: 14px;
    border-radius: 4px;

    &:first-child {
      margin-left: 0;
    }
  }
}
</style>