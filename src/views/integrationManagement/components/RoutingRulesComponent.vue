<template>
  <div class="routing-rules-container">
    <!-- 左侧路由规则编辑区域 -->
    <div class="routing-editor-panel">
      <div class="panel-header">
        <h3>路由规则</h3>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="showAddRuleDialog">
            <el-icon><Plus /></el-icon>添加规则
          </el-button>
        </div>
      </div>

      <!-- 路由规则说明 -->
      <div class="routing-description">
        <p class="description-text">
          <i class="el-icon-info"></i>
          根据 标题、严重程度和标签 等条件，将告警事件路由到不同的协作空间。
        </p>
      </div>

      <div class="rules-container">
        <div v-if="!routingRules.cases || !routingRules.cases.length" class="empty-rules">
          <el-empty description="暂无路由规则" />
          <el-button type="primary" @click="showAddRuleDialog">添加规则</el-button>
        </div>

        <div v-else class="rule-list">
          <!-- 路由规则卡片 -->
          <div v-for="(rule, index) in routingRules.cases" :key="index" class="rule-card">
            <div class="rule-header">
              <div class="rule-title">
                <span class="rule-number">规则{{ index + 1 }}</span>
              </div>
              <div class="rule-actions">
                <el-button type="primary" size="small" @click="editRule(index)" class="action-btn">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="info" size="small" @click="moveRule(index, 'up')" :disabled="index === 0" class="action-btn">
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button type="info" size="small" @click="moveRule(index, 'down')" :disabled="index === routingRules.cases.length - 1" class="action-btn">
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <el-button type="danger" size="small" @click="removeRule(index)" class="action-btn">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="rule-content">
              <div class="rule-summary">
                <div class="rule-condition" v-if="rule.if && rule.if.length">
                  <span class="condition-label">如果</span>
                  <div class="condition-code">
                    {{ formatCondition(rule.if) }}
                  </div>
                </div>
                <div class="rule-description">
                  <span>将告警投递到空间</span>
                  <div class="channel-tags">
                    <el-tag v-for="channelId in rule.channel_ids" :key="channelId" size="small" class="channel-tag">
                      {{ getChannelName(channelId) }}
                    </el-tag>
                  </div>
                </div>
                <div class="rule-fallthrough" v-if="rule.fallthrough">
                  <span class="fallthrough-label">继续匹配下一条规则</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 默认路由规则 -->
          <div class="rule-card default-rule">
            <div class="rule-header">
              <div class="rule-title">
                <span class="rule-number">默认路由</span>
              </div>
              <div class="rule-actions">
                <el-button type="primary" size="small" @click="editDefaultRule" class="action-btn">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="rule-content">
              <div class="rule-summary">
                <div class="rule-description">
                  <span>如果未匹配到任何路由，将告警发送给协作空间</span>
                  <div class="channel-tags">
                    <el-tag v-for="channelId in routingRules.default.channel_ids" :key="channelId" size="small" class="channel-tag">
                      {{ getChannelName(channelId) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 自动保存提示 -->
      <div class="save-actions">
        <div class="auto-save-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>所有修改将自动保存</span>
        </div>
      </div>
    </div>

    <!-- 右侧告警关联区域 -->
    <AlertAssociationComponent :integration-id="props.integrationId" />

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialogVisible"
      :title="isEditMode ? '编辑规则' : '添加规则'"
      width="600px"
      destroy-on-close
      :close-on-click-modal="false"
      class="rule-dialog">
      <div class="rule-dialog-content">
        <el-form :model="currentRule" label-position="top">
          <!-- 条件设置 -->
          <el-form-item label="条件">
            <div v-for="(condition, idx) in currentRule.if" :key="idx" class="condition-item">
              <div class="condition-row">
                <el-select v-model="condition.key" placeholder="选择字段" class="field-select">
                  <el-option label="告警级别" value="severity" />
                  <el-option label="标题" value="title" />
                  <el-option label="描述" value="description" />
                  <el-option label="告警名称" value="alertname" />
                  <el-option label="实例" value="instance" />
                  <el-option label="服务" value="service" />
                </el-select>
                <el-select v-model="condition.oper" placeholder="选择操作符" class="operator-select">
                  <el-option label="等于" value="EQ" />
                  <el-option label="被包含" value="IN" />
                  <el-option label="包含" value="CONTAINS" />
                  <el-option label="不等于" value="NOT_EQ" />
                </el-select>
                <el-select v-model="condition.vals" multiple placeholder="输入值" class="value-select" collapse-tags>
                  <template v-if="condition.key === 'severity'">
                    <el-option label="Critical" value="Critical" />
                    <el-option label="Warning" value="Warning" />
                    <el-option label="Info" value="Info" />
                  </template>
                  <template v-else>
                    <el-option label="test*" value="test*" />
                    <el-option label="*error" value="*error" />
                    <el-option label="^prod-.*" value="^prod-.*" />
                    <el-option label="api-gateway" value="api-gateway" />
                  </template>
                </el-select>
                <el-button @click="removeCondition(idx)" type="danger" circle plain size="small" class="delete-btn" style="border: none; background-color: transparent;">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="add-condition">
              <el-button type="primary" plain size="small" @click="addCondition" class="add-condition-btn">
                <el-icon><Plus /></el-icon>添加条件
              </el-button>
            </div>
            <div class="form-tip">根据标题、严重程度和标签等条件，将告警事件路由到不同的协作空间。匹配值支持正则表达式和通配符（如 test*、*error、^prod-.*）</div>
          </el-form-item>

          <!-- 协作空间设置 -->
          <el-form-item label="将其投递到协作空间">
            <el-select v-model="currentRule.channel_ids" multiple placeholder="选择协作空间" style="width: 100%">
              <el-option
                v-for="channel in channels"
                :key="channel.id"
                :label="channel.name"
                :value="channel.id">
                <div class="channel-option">
                  <span>{{ channel.name }}</span>
                  <span class="channel-type">{{ channel.type }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 继续匹配设置 -->
          <el-form-item>
            <el-checkbox v-model="currentRule.fallthrough">匹配后继续执行下一条规则</el-checkbox>
            <div class="form-tip">启用此选项后，即使匹配了当前规则，系统仍会继续检查后续规则</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCurrentRule" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑默认规则对话框 -->
    <el-dialog
      v-model="defaultRuleDialogVisible"
      title="编辑默认路由"
      width="600px"
      destroy-on-close
      :close-on-click-modal="false"
      class="rule-dialog">
      <div class="rule-dialog-content">
        <el-form :model="defaultRule" label-position="top">
          <!-- 协作空间设置 -->
          <el-form-item label="将其投递到协作空间">
            <el-select v-model="defaultRule.channel_ids" multiple placeholder="选择协作空间" style="width: 100%">
              <el-option
                v-for="channel in channels"
                :key="channel.id"
                :label="channel.name"
                :value="channel.id">
                <div class="channel-option">
                  <span>{{ channel.name }}</span>
                  <span class="channel-type">{{ channel.type }}</span>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">当告警不匹配任何规则时，将发送给这些协作空间</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="defaultRuleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDefaultRule" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit, ArrowUp, ArrowDown, InfoFilled } from '@element-plus/icons-vue';
import { getRoutingRules, updateRoutingRules, getChannels } from '@/api/alertRouteAdapter';
import AlertAssociationComponent from './AlertAssociationComponent.vue';

const props = defineProps({
  integrationId: {
    type: [Number, String],
    required: true
  }
});

// 路由规则数据
const routingRules = ref({
  id: null,
  integrationId: null,
  cases: [],
  default: {
    channel_ids: []
  },
  status: 'enabled',
  version: 1,
  updated_by: 1,
  creator_id: 1,
  created_at: Math.floor(Date.now() / 1000),
  updated_at: Math.floor(Date.now() / 1000)
});

// 通知渠道数据
const channels = ref([]);

// 对话框控制
const ruleDialogVisible = ref(false);
const defaultRuleDialogVisible = ref(false);
const isEditMode = ref(false);
const currentRule = ref(null);
const currentRuleIndex = ref(-1);
const defaultRule = ref({ channel_ids: [] });
const saving = ref(false);

// 格式化条件显示
const formatCondition = (conditions) => {
  if (!conditions || !conditions.length) return '';

  return conditions.map(condition => {
    const fieldMap = {
      'title': '标题',
      'description': '描述',
      'severity': '告警级别',
      'alertname': '告警名称',
      'instance': '实例',
      'service': '服务'
    };

    const operMap = {
      'EQ': '等于',
      'IN': '被包含',
      'CONTAINS': '包含',
      'NOT_EQ': '不等于'
    };

    const field = fieldMap[condition.key] || condition.key;
    const oper = operMap[condition.oper] || condition.oper;
    const vals = condition.vals.join(', ');

    return `${field} ${oper} ${vals}`;
  }).join(' 且 ');
};

// 获取通知渠道名称
const getChannelName = (channelId) => {
  const channel = channels.value.find(c => c.id === channelId);
  return channel ? channel.name : `渠道 ${channelId}`;
};

// 显示添加规则对话框
const showAddRuleDialog = () => {
  isEditMode.value = false;
  currentRule.value = {
    if: [
      {
        key: 'severity',
        oper: 'EQ',
        vals: ['Critical']
      }
    ],
    channel_ids: [],
    fallthrough: true
  };
  ruleDialogVisible.value = true;
};

// 编辑规则
const editRule = (index) => {
  isEditMode.value = true;
  currentRuleIndex.value = index;
  currentRule.value = JSON.parse(JSON.stringify(routingRules.value.cases[index]));

  // 确保规则有必要的字段
  if (!currentRule.value.if) {
    currentRule.value.if = [
      {
        key: 'severity',
        oper: 'EQ',
        vals: ['Critical']
      }
    ];
  }

  if (!currentRule.value.channel_ids) {
    currentRule.value.channel_ids = [];
  }

  if (currentRule.value.fallthrough === undefined) {
    currentRule.value.fallthrough = true;
  }

  ruleDialogVisible.value = true;
};

// 编辑默认规则
const editDefaultRule = () => {
  defaultRule.value = JSON.parse(JSON.stringify(routingRules.value.default));
  defaultRuleDialogVisible.value = true;
};

// 添加条件
const addCondition = () => {
  if (!currentRule.value.if) {
    currentRule.value.if = [];
  }

  currentRule.value.if.push({
    key: 'title',
    oper: 'CONTAINS',
    vals: ['*error']
  });
};

// 移除条件
const removeCondition = (index) => {
  currentRule.value.if.splice(index, 1);
};

// 移动规则
const moveRule = async (index, direction) => {
  const rules = routingRules.value.cases;
  let moved = false;

  // 备份原始顺序，以便回滚
  const originalRules = JSON.parse(JSON.stringify(rules));

  if (direction === 'up' && index > 0) {
    // 向上移动
    [rules[index], rules[index - 1]] = [rules[index - 1], rules[index]];
    moved = true;
  } else if (direction === 'down' && index < rules.length - 1) {
    // 向下移动
    [rules[index], rules[index + 1]] = [rules[index + 1], rules[index]];
    moved = true;
  }

  if (moved) {
    // 立即调用API保存到服务器
    try {
      await saveRoutingRules();
      ElMessage.success('规则顺序已更新');
    } catch (error) {
      // 如果保存失败，回滚移动操作
      routingRules.value.cases = originalRules;
      ElMessage.error('移动失败，操作已回滚');
    }
  }
};

// 移除规则
const removeRule = (index) => {
  ElMessageBox.confirm('确定要删除这条规则吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 备份要删除的规则，以便回滚
    const deletedRule = routingRules.value.cases[index];

    // 从本地数据中删除
    routingRules.value.cases.splice(index, 1);

    // 立即调用API保存到服务器
    try {
      await saveRoutingRules();
      ElMessage.success('规则已删除');
    } catch (error) {
      // 如果保存失败，回滚删除操作
      routingRules.value.cases.splice(index, 0, deletedRule);
      ElMessage.error('删除失败，操作已回滚');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 切换路由规则状态已移除

// 保存当前编辑的规则
const saveCurrentRule = async () => {
  // 验证规则
  if (!currentRule.value.if || currentRule.value.if.length === 0) {
    ElMessage.warning('请至少添加一个条件');
    return;
  }

  if (!currentRule.value.channel_ids || currentRule.value.channel_ids.length === 0) {
    ElMessage.warning('请选择至少一个协作空间');
    return;
  }

  // 保存规则到本地数据
  if (isEditMode.value) {
    routingRules.value.cases[currentRuleIndex.value] = currentRule.value;
  } else {
    routingRules.value.cases.push(currentRule.value);
  }

  ruleDialogVisible.value = false;

  // 立即调用API保存到服务器
  try {
    await saveRoutingRules();
    ElMessage.success(isEditMode.value ? '规则已更新' : '规则已添加');
  } catch (error) {
    // 如果保存失败，回滚本地操作
    if (isEditMode.value) {
      // 重新加载数据以恢复原状态
      await loadRoutingRules();
    } else {
      // 移除刚添加的规则
      routingRules.value.cases.pop();
    }
    ElMessage.error('保存失败，操作已回滚');
  }
};

// 保存默认规则
const saveDefaultRule = async () => {
  const originalDefault = JSON.parse(JSON.stringify(routingRules.value.default));

  // 保存到本地数据
  routingRules.value.default = defaultRule.value;
  defaultRuleDialogVisible.value = false;

  // 立即调用API保存到服务器
  try {
    await saveRoutingRules();
    ElMessage.success('默认路由已更新');
  } catch (error) {
    // 如果保存失败，回滚本地操作
    routingRules.value.default = originalDefault;
    ElMessage.error('保存失败，操作已回滚');
  }
};

// 保存路由规则到服务器
const saveRoutingRules = async () => {
  saving.value = true;
  try {
    console.log('💾 保存路由规则，集成ID:', props.integrationId);
    console.log('💾 路由规则数据:', routingRules.value);

    const { success } = await updateRoutingRules(props.integrationId, routingRules.value);
    if (success) {
      console.log('✅ 路由规则保存成功');
      // 重新加载数据以获取最新状态（包括更新后的版本号）
      await loadRoutingRules();
    } else {
      throw new Error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存路由规则失败:', error);
    throw error; // 重新抛出错误，让调用方处理
  } finally {
    saving.value = false;
  }
};

// 加载路由规则
const loadRoutingRules = async () => {
  try {
    console.log('🔍 加载路由规则，集成ID:', props.integrationId);
    const { success, data } = await getRoutingRules(props.integrationId);
    if (success) {
      console.log('✅ 路由规则加载成功:', data);
      routingRules.value = data;

      // 确保cases和default字段存在
      if (!routingRules.value.cases) {
        routingRules.value.cases = [];
      }

      if (!routingRules.value.default) {
        routingRules.value.default = {
          channel_ids: []
        };
      }

      // 确保integrationId字段正确设置
      routingRules.value.integrationId = props.integrationId;
    }
  } catch (error) {
    console.error('加载路由规则失败:', error);
    ElMessage.error(error.message || '加载路由规则失败');
  }
};

// 加载通知渠道
const loadChannels = async () => {
  try {
    const { success, data } = await getChannels();
    if (success) {
      channels.value = data;
    }
  } catch (error) {
    console.error('加载通知渠道失败:', error);
    ElMessage.error('加载通知渠道失败');
  }
};

// 监听集成ID变化
watch(() => props.integrationId, () => {
  loadRoutingRules();
}, { immediate: true });

// 组件挂载时加载数据
onMounted(() => {
  loadRoutingRules();
  loadChannels();
});
</script>

<style lang="scss" scoped>
.routing-rules-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  color: #303133;
}

.routing-editor-panel {
  width: 50%;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.rules-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-rules {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;
}

.rule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.rule-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-number {
  font-weight: 600;
  color: #303133;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    opacity: 0.8;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.rule-content {
  padding: 16px;
}

.rule-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-condition {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.condition-label {
  font-weight: 600;
  color: #606266;
}

.condition-code {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  color: #606266;
}

.rule-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  color: #606266;
}

.routing-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  color: #606266;
  padding: 0 16px 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.description-text {
  color: #909399;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.channel-tag {
  margin-right: 0;
}

.rule-fallthrough {
  margin-top: 8px;
  color: #909399;
  font-size: 13px;
  font-style: italic;
}

.default-rule {
  .rule-header {
    background-color: #ecf5ff;
  }
}

.save-actions {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.auto-save-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 13px;

  .el-icon {
    color: #409eff;
  }
}

.rule-dialog-content {
  padding: 0;
}

.condition-item {
  margin-bottom: 8px;
}

.condition-row {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.condition-separator {
  color: #c0c4cc;
  font-size: 14px;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.field-select {
  width: 150px;
  flex-shrink: 0;
  margin-right: 0;
}

.operator-select {
  width: 150px;
  flex-shrink: 0;
  margin-right: 0;
}

.value-select {
  width: 150px;
  flex-shrink: 0;
  margin-right: 0;
}

.add-condition {
  margin-top: 16px;
}

.add-condition-btn {
  width: 100%;
}

.delete-btn {
  flex-shrink: 0;
  color: #f56c6c;

  &:hover {
    background-color: rgba(245, 108, 108, 0.1);
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.5;
}

.channel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-type {
  color: #909399;
  font-size: 12px;
}

:deep(.rule-dialog) {
  .el-dialog__header {
    padding: 16px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  .el-dialog__title {
    font-weight: 600;
    font-size: 16px;
  }

  .el-dialog__body {
    padding: 20px 20px 10px;
    background-color: #f9f9f9;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #fff;
    text-align: right;
  }

  .el-select {
    width: 100%;
  }

  .el-select-dropdown__item {
    padding: 0 15px;
    height: 34px;
    line-height: 34px;
  }

  .el-select .el-select__tags {
    padding-left: 4px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
    padding-bottom: 8px;
  }

  .el-form-item {
    margin-bottom: 22px;
  }
}
</style>
