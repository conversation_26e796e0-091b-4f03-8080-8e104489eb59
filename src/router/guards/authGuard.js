/**
 * 认证路由守卫
 * 在进入路由前检查用户认证状态
 */
import { useAuthStore } from '@/stores/modules/authStore'

/**
 * 不需要认证的路由白名单
 */
const AUTH_WHITELIST = [
  '/login',
  '/404',
  '/403',
  '/500',
  '/remind'
]

/**
 * 检查路由是否在白名单中
 * @param {string} path - 路由路径
 * @returns {boolean} 是否在白名单中
 */
function isInWhitelist(path) {
  return AUTH_WHITELIST.some(whitePath => {
    if (whitePath === path) return true
    // 支持通配符匹配
    if (whitePath.endsWith('*')) {
      const prefix = whitePath.slice(0, -1)
      return path.startsWith(prefix)
    }
    return false
  })
}

/**
 * 认证守卫函数
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export async function authGuard(to, from, next) {
  const authStore = useAuthStore()
  
  console.log('🛡️ 路由守卫检查:', to.path)

  // 检查是否在白名单中
  if (isInWhitelist(to.path)) {
    console.log('✅ 路由在白名单中，直接通过')
    next()
    return
  }

  try {
    // 首先尝试从本地存储恢复认证状态
    const hasLocalAuth = authStore.restoreAuthFromStorage()
    
    if (hasLocalAuth && authStore.isTokenValid) {
      console.log('✅ 本地认证有效，直接通过')
      next()
      return
    }

    // 如果本地认证无效，尝试从Cookie登录
    console.log('🔄 本地认证无效，尝试Cookie登录...')
    const loginResult = await authStore.loginWithCookie()
    
    if (loginResult.success) {
      console.log('✅ Cookie登录成功，允许访问')
      next()
    } else {
      console.log('❌ Cookie登录失败，跳转到登录页')
      handleAuthFailure(to, next)
    }
  } catch (error) {
    console.error('❌ 认证守卫执行失败:', error.message)
    handleAuthFailure(to, next)
  }
}

/**
 * 处理认证失败的情况
 * @param {Object} to - 目标路由
 * @param {Function} next - 路由跳转函数
 */
function handleAuthFailure(to, next) {
  // 清除可能存在的无效认证信息
  const authStore = useAuthStore()
  authStore.clearAuth()
  
  // 如果当前就在登录页，避免无限重定向
  if (to.path === '/login') {
    next()
    return
  }
  
  // 跳转到登录页，并保存原始目标路径
  next({
    path: '/login',
    query: {
      redirect: to.fullPath
    }
  })
}

/**
 * 登录后重定向处理
 * @param {Object} route - 当前路由对象
 * @param {Object} router - 路由器实例
 */
export function handleLoginRedirect(route, router) {
  const redirectPath = route.query.redirect || '/'
  
  // 清除redirect参数
  const query = { ...route.query }
  delete query.redirect
  
  router.replace({
    path: redirectPath,
    query: Object.keys(query).length > 0 ? query : undefined
  })
}

/**
 * 权限检查守卫（可选）
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export function permissionGuard(to, from, next) {
  const authStore = useAuthStore()
  
  // 检查路由是否需要特定权限
  const requiredRole = to.meta?.requireRole
  const requiredPermission = to.meta?.requirePermission
  
  if (requiredRole && authStore.userRole !== requiredRole) {
    console.log('❌ 用户角色不足，无法访问')
    next('/403')
    return
  }
  
  if (requiredPermission && !authStore.userInfo?.permissions?.includes(requiredPermission)) {
    console.log('❌ 用户权限不足，无法访问')
    next('/403')
    return
  }
  
  next()
}

export default authGuard
