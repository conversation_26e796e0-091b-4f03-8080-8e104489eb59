import { createRouter, createWebHashHistory } from "vue-router";
// 这个是布局组件，如果我们项目不需要使用内置布局则可以删除掉
import Layout from "./../layout/layoutIndex.vue";
import errorPages from "./modules/errorPages";
import { authGuard } from "./guards/authGuard";
// import templateManagement from "./modules/templateManagement";
// import oncallPages from "./modules/oncallPages"; // 暂未使用

// 固定路由，一般不会根据权限或者用户信息改变的路由
export const constantRoutes = [
  {
    path: "/",
    name: "mainpage",
    redirect: "/oncall/integration/integrationpageindex",
    component: window.__MICRO_APP_ENVIRONMENT__ ? null : Layout,
    // children: [
    //   {
    //     path: "mainpageindex",
    //     name: "mainpageindex",
    //     component: () => import("./../views/mainPage.vue"),
    //   },
    // ],
    hidden: true,
  },
  {
    path: "/login",
    name: "login",
    component: () => import("../views/auth/LoginPage.vue"),
    meta: {
      title: "登录",
      hidden: true
    },
    hidden: true,
  },
  // {
  //   path: "/home",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "introduce",
  //       component: () => import("../views/home/<USER>"),
  //       name: "Introduce",
  //       meta: { title: "介绍页", icon: "tcicon-home" },
  //     },
  //   ],
  // },
  // {
  //   path: "/demo",
  //   component: Layout,
  //   meta: {
  //     title: "功能示例",
  //     icon: "tcicon-baby-app",
  //   },
  //   alwaysShow: true,
  //   children: [
  //     {
  //       path: "tech",
  //       component: () => import("../views/demo/techView.vue"),
  //       name: "tech",
  //       meta: {
  //         title: "获取用户信息",
  //         icon: "tcicon-people",
  //         // sysCode: "Tech",
  //         // resCode: "TechWorkbench",
  //       },
  //     },
  //     {
  //       path: "bus",
  //       component: () => import("../views/demo/busView.vue"),
  //       name: "bus",
  //       meta: {
  //         title: "事件总线使用",
  //         icon: "tcicon-speaker-one",
  //         // sysCode: "TCvue",
  //         // resCode: "resCode1",
  //       },
  //     },
  //     {
  //       path: "auth",
  //       component: () => import("../views/demo/authView.vue"),
  //       name: "auth",
  //       meta: {
  //         title: "权限控制示例",
  //         icon: "tcicon-permissions",
  //         // sysCode: "TCvue",
  //         // resCode: "resCode1",
  //       },
  //     }
  //   ],
  // },
  {
    path: "/oncall/space",
    component: Layout,
    redirect: "/oncall/space/spacepageindex",
    children: [
      {
        path: "spacepageindex",
        name: "spacepageindex",
        meta: {
          title: "团队空间",
          icon: "tcicon-curling",
        },
        component: () => import("../views/spaceManagement/index.vue"),
      },
      {
        path: "detail/:id",
        name: "spaceDetail",
        meta: {
          title: "空间详情",
          activeMenu: "/oncall/space/spacepageindex",
          hidden: true
        },
        hidden: true,
        component: () => import("../views/spaceManagement/detail.vue"),
      },
      {
        path: "noise-reduction",
        name: "noiseReduction",
        meta: {
          title: "降噪配置",
          activeMenu: "/oncall/space/spacepageindex",
          hidden: true
        },
        hidden: true,
        component: () => import("../views/spaceManagement/NoiseReductionPage.vue"),
      }
    ]
  },
  {
    path: "/oncall/msg",
    component: Layout,
    redirect: "/oncall/msg/msgpageindex",
    children: [
      {
        path: "msgpageindex",
        name: "msgpageindex",
        meta: {
          title: "消息中心",
          icon: "tcicon-message",
          },
        component: () => import("../views/msgManagement/index.vue"),
      },
      {
        path: "detail/:id",
        name: "messageDetail",
        meta: {
          title: "消息详情",
          activeMenu: "/oncall/msg/msgpageindex",
          hidden: true
        },
        hidden: true,
        component: () => import("../views/msgManagement/detail.vue"),
      },
    ]
  },
  {
    path: "/alert-detail/:id",
    component: Layout,
    children: [
      {
        path: "",
        name: "alertDetail",
        meta: {
          title: "告警详情",
          activeMenu: "/oncall/msg/msgpageindex",
          hidden: true
        },
        component: () => import("../views/msgManagement/AlertDetail.vue"),
        props: { isPage: true }
      },
    ],
    hidden: true
  },
  {
    path: "/oncall/integration",
    component: Layout,
    meta: {
      title: "集成管理",
      icon: "tcicon-cycle-movement",
    },
    children: [
      {
        path: "integrationpageindex",
        name: "integrationpageindex",
        meta: {
          title: "集成管理",
          icon: "tcicon-cycle-movement",
        },
        component: () => import("../views/integrationManagement/index.vue"),
      },
      {
        path: "add/:type?",
        name: "integrationAdd",
        meta: {
          title: "添加集成",
          activeMenu: "/oncall/integration/integrationpageindex",
          hidden: true
        },
        hidden: true,
        component: () => import("../views/integrationManagement/AddIntegration.vue"),
      },
      {
        path: "detail/:type/:id",
        name: "integrationDetail",
        meta: {
          title: "集成详情",
          activeMenu: "/oncall/integration/integrationpageindex",
          hidden: true
        },
        hidden: true,
        component: () => import("../views/integrationManagement/DetailIntegration.vue"),
      }
    ]
  },
  {
      path: "/oncall/notify",
      component: Layout,
      meta: {
      title: "通知中心",
      icon: "tcicon-comment",
      },
      children: [
      {
          path: "channel",
          component: () => import("../views/notifyMangement/sourceManagement/index.vue"),
          name: "channel",
          meta: {
          title: "渠道管理",
          icon: "tcicon-circular-connection",
          // sysCode: "Tech",
          // resCode: "TechWorkbench",
          },
      },
      {
        path: "template",
        component: () => import('../views/notifyMangement/templateManagement/index.vue'),
        name: 'TemplateManagement',
        meta: {
          title: '模板管理',
          icon: 'tcicon-page-template'
        }
      },
      {
        path: "template/detail/:id",
        component: () => import('../views/notifyMangement/templateManagement/detail/index.vue'),
        name: 'TemplateDetail',
        meta: {
          title: '模板详情',
          activeMenu: '/oncall/notify/template'
        },
        hidden: true
      }
      ]
  },
  {
      path: "/oncall/duty",
      component: Layout,
      redirect: "/oncall/duty/dutypageindex",
      children: [
        {
          path: "dutypageindex",
          name: "dutypageindex",
          meta: {
            title: "值班管理",
            icon: "tcicon-baby-app",
          },
          component: () => import("../views/demo/emptyView.vue"),
        }
      ]
  },
  errorPages,
];

// 动态路由， 根据获取到的用户权限或者配置改变的路由
export const asyncRoutes = [

];


const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: constantRoutes,
});

// 添加全局前置守卫
router.beforeEach(authGuard);

export default router;
