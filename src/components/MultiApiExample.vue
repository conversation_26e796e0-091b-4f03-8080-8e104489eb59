<template>
  <div class="multi-api-example">
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>多API服务调用示例</span>
          <el-button type="primary" @click="testAllServices">测试所有服务</el-button>
        </div>
      </template>

      <!-- 服务状态显示 -->
      <div class="service-status">
        <h3>服务状态</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="status-card" :class="{ 'status-healthy': oncallStatus, 'status-error': !oncallStatus }">
              <div class="status-content">
                <h4>OnCall服务</h4>
                <p>端口: 2002</p>
                <el-tag :type="oncallStatus ? 'success' : 'danger'">
                  {{ oncallStatus ? '正常' : '异常' }}
                </el-tag>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="status-card" :class="{ 'status-healthy': systemStatus, 'status-error': !systemStatus }">
              <div class="status-content">
                <h4>System服务</h4>
                <p>端口: 2001</p>
                <el-tag :type="systemStatus ? 'success' : 'danger'">
                  {{ systemStatus ? '正常' : '异常' }}
                </el-tag>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="status-card" :class="{ 'status-healthy': messageStatus, 'status-error': !messageStatus }">
              <div class="status-content">
                <h4>Message服务</h4>
                <p>端口: 2003</p>
                <el-tag :type="messageStatus ? 'success' : 'danger'">
                  {{ messageStatus ? '正常' : '异常' }}
                </el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- API调用示例 -->
      <div class="api-examples">
        <h3>API调用示例</h3>
        
        <!-- OnCall服务示例 -->
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="OnCall服务 - 值班管理" name="oncall">
            <div class="example-section">
              <el-button @click="testOncallSchedules" :loading="loading.oncall">
                获取值班计划
              </el-button>
              <el-button @click="testOncallUsers" :loading="loading.oncall">
                获取值班人员
              </el-button>
              <el-button @click="testOncallStatistics" :loading="loading.oncall">
                获取值班统计
              </el-button>
              
              <div v-if="results.oncall" class="result-display">
                <h4>响应结果:</h4>
                <pre>{{ JSON.stringify(results.oncall, null, 2) }}</pre>
              </div>
            </div>
          </el-collapse-item>

          <el-collapse-item title="System服务 - 系统管理" name="system">
            <div class="example-section">
              <el-button @click="testSystemUsers" :loading="loading.system">
                获取用户列表
              </el-button>
              <el-button @click="testSystemRoles" :loading="loading.system">
                获取角色列表
              </el-button>
              <el-button @click="testSystemConfig" :loading="loading.system">
                获取系统配置
              </el-button>
              
              <div v-if="results.system" class="result-display">
                <h4>响应结果:</h4>
                <pre>{{ JSON.stringify(results.system, null, 2) }}</pre>
              </div>
            </div>
          </el-collapse-item>

          <el-collapse-item title="Message服务 - 消息通知" name="message">
            <div class="example-section">
              <el-button @click="testMessageTemplates" :loading="loading.message">
                获取消息模板
              </el-button>
              <el-button @click="testNotificationChannels" :loading="loading.message">
                获取通知渠道
              </el-button>
              <el-button @click="testMessageHistory" :loading="loading.message">
                获取消息历史
              </el-button>
              
              <div v-if="results.message" class="result-display">
                <h4>响应结果:</h4>
                <pre>{{ JSON.stringify(results.message, null, 2) }}</pre>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 批量操作示例 -->
      <div class="batch-examples">
        <h3>批量操作示例</h3>
        <el-button @click="testBatchRequests" :loading="loading.batch">
          批量请求所有服务
        </el-button>
        
        <div v-if="batchResults.length > 0" class="batch-results">
          <h4>批量请求结果:</h4>
          <el-table :data="batchResults" style="width: 100%">
            <el-table-column prop="service" label="服务" width="120" />
            <el-table-column prop="endpoint" label="接口" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="responseTime" label="响应时间(ms)" width="120" />
            <el-table-column prop="message" label="消息" />
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 导入API函数
import { 
  getOncallSchedules, 
  getOncallUsers, 
  getOncallStatistics 
} from '@/api/oncallApi'

import { 
  getUserList, 
  getRoleList, 
  getSystemConfig 
} from '@/api/systemApi'

import { 
  getMessageTemplates, 
  getNotificationChannels, 
  getMessageHistory 
} from '@/api/messageApi'

import { 
  checkServiceHealth, 
  serviceApiCall, 
  SERVICE_TYPES 
} from '@/utils/multiRequest'

// 响应式数据
const activeCollapse = ref(['oncall'])
const oncallStatus = ref(false)
const systemStatus = ref(false)
const messageStatus = ref(false)

const loading = reactive({
  oncall: false,
  system: false,
  message: false,
  batch: false
})

const results = reactive({
  oncall: null,
  system: null,
  message: null
})

const batchResults = ref([])

// 检查服务健康状态
const checkAllServicesHealth = async () => {
  try {
    oncallStatus.value = await checkServiceHealth(SERVICE_TYPES.ONCALL)
    systemStatus.value = await checkServiceHealth(SERVICE_TYPES.SYSTEM)
    messageStatus.value = await checkServiceHealth(SERVICE_TYPES.MESSAGE)
  } catch (error) {
    console.error('健康检查失败:', error)
  }
}

// OnCall服务测试
const testOncallSchedules = async () => {
  loading.oncall = true
  try {
    const response = await getOncallSchedules({ 
      spaceId: 'test-space',
      page: 1,
      pageSize: 10
    })
    results.oncall = response
    ElMessage.success('OnCall值班计划获取成功')
  } catch (error) {
    ElMessage.error(`OnCall服务调用失败: ${error.message}`)
    results.oncall = { error: error.message }
  } finally {
    loading.oncall = false
  }
}

const testOncallUsers = async () => {
  loading.oncall = true
  try {
    const response = await getOncallUsers({ 
      scheduleId: 'test-schedule'
    })
    results.oncall = response
    ElMessage.success('OnCall值班人员获取成功')
  } catch (error) {
    ElMessage.error(`OnCall服务调用失败: ${error.message}`)
    results.oncall = { error: error.message }
  } finally {
    loading.oncall = false
  }
}

const testOncallStatistics = async () => {
  loading.oncall = true
  try {
    const response = await getOncallStatistics({
      spaceId: 'test-space',
      startTime: '2024-01-01',
      endTime: '2024-01-31'
    })
    results.oncall = response
    ElMessage.success('OnCall统计数据获取成功')
  } catch (error) {
    ElMessage.error(`OnCall服务调用失败: ${error.message}`)
    results.oncall = { error: error.message }
  } finally {
    loading.oncall = false
  }
}

// System服务测试
const testSystemUsers = async () => {
  loading.system = true
  try {
    const response = await getUserList({
      page: 1,
      pageSize: 10,
      keyword: ''
    })
    results.system = response
    ElMessage.success('System用户列表获取成功')
  } catch (error) {
    ElMessage.error(`System服务调用失败: ${error.message}`)
    results.system = { error: error.message }
  } finally {
    loading.system = false
  }
}

const testSystemRoles = async () => {
  loading.system = true
  try {
    const response = await getRoleList({
      page: 1,
      pageSize: 10
    })
    results.system = response
    ElMessage.success('System角色列表获取成功')
  } catch (error) {
    ElMessage.error(`System服务调用失败: ${error.message}`)
    results.system = { error: error.message }
  } finally {
    loading.system = false
  }
}

const testSystemConfig = async () => {
  loading.system = true
  try {
    const response = await getSystemConfig('general')
    results.system = response
    ElMessage.success('System配置获取成功')
  } catch (error) {
    ElMessage.error(`System服务调用失败: ${error.message}`)
    results.system = { error: error.message }
  } finally {
    loading.system = false
  }
}

// Message服务测试
const testMessageTemplates = async () => {
  loading.message = true
  try {
    const response = await getMessageTemplates({
      page: 1,
      pageSize: 10,
      type: 'email'
    })
    results.message = response
    ElMessage.success('Message模板列表获取成功')
  } catch (error) {
    ElMessage.error(`Message服务调用失败: ${error.message}`)
    results.message = { error: error.message }
  } finally {
    loading.message = false
  }
}

const testNotificationChannels = async () => {
  loading.message = true
  try {
    const response = await getNotificationChannels({
      page: 1,
      pageSize: 10
    })
    results.message = response
    ElMessage.success('Message通知渠道获取成功')
  } catch (error) {
    ElMessage.error(`Message服务调用失败: ${error.message}`)
    results.message = { error: error.message }
  } finally {
    loading.message = false
  }
}

const testMessageHistory = async () => {
  loading.message = true
  try {
    const response = await getMessageHistory({
      page: 1,
      pageSize: 10,
      startTime: '2024-01-01',
      endTime: '2024-01-31'
    })
    results.message = response
    ElMessage.success('Message历史记录获取成功')
  } catch (error) {
    ElMessage.error(`Message服务调用失败: ${error.message}`)
    results.message = { error: error.message }
  } finally {
    loading.message = false
  }
}

// 批量请求测试
const testBatchRequests = async () => {
  loading.batch = true
  batchResults.value = []
  
  const requests = [
    {
      service: 'OnCall',
      endpoint: '/api/v1/oncall/schedules',
      call: () => getOncallSchedules({ spaceId: 'test' })
    },
    {
      service: 'System', 
      endpoint: '/api/v1/system/users',
      call: () => getUserList({ page: 1, pageSize: 5 })
    },
    {
      service: 'Message',
      endpoint: '/api/v1/message/templates', 
      call: () => getMessageTemplates({ page: 1, pageSize: 5 })
    }
  ]
  
  for (const request of requests) {
    const startTime = Date.now()
    try {
      await request.call()
      const responseTime = Date.now() - startTime
      batchResults.value.push({
        service: request.service,
        endpoint: request.endpoint,
        status: 'success',
        responseTime,
        message: '请求成功'
      })
    } catch (error) {
      const responseTime = Date.now() - startTime
      batchResults.value.push({
        service: request.service,
        endpoint: request.endpoint,
        status: 'error',
        responseTime,
        message: error.message
      })
    }
  }
  
  loading.batch = false
  ElMessage.success('批量请求完成')
}

// 测试所有服务
const testAllServices = async () => {
  await checkAllServicesHealth()
  ElMessage.success('服务健康检查完成')
}

// 组件挂载时检查服务状态
onMounted(() => {
  checkAllServicesHealth()
})
</script>

<style scoped>
.multi-api-example {
  padding: 20px;
}

.example-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-status {
  margin-bottom: 30px;
}

.status-card {
  text-align: center;
  transition: all 0.3s ease;
}

.status-card.status-healthy {
  border-color: #67c23a;
}

.status-card.status-error {
  border-color: #f56c6c;
}

.status-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.status-content p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.example-section {
  padding: 20px 0;
}

.example-section .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.result-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result-display h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.result-display pre {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.batch-examples {
  margin-top: 30px;
}

.batch-results {
  margin-top: 20px;
}

.batch-results h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
