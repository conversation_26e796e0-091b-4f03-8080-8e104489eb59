import Cookies from "js-cookie";
import * as Settings from "./../settings";
import settings from './../settings'
const TokenKey = Settings.default.projectName + "Admin-Token";
const ServiceurlKey = Settings.default.projectName + "Key";
export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}
export function setServiceurl(service) {
  return Cookies.set(ServiceurlKey, service);
}

export function getServiceurl() {
  return Settings.default.mainServerUrl;
}
