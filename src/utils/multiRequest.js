/**
 * 多服务API请求工具
 * 支持OnCall、System、Message三个后端服务的统一管理
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
import axios from "axios";
import * as Settings from "./../settings";

/**
 * 服务类型枚举
 */
export const SERVICE_TYPES = {
  ONCALL: 'oncall',
  SYSTEM: 'system',
  MESSAGE: 'message'
};

/**
 * 创建axios实例的工厂函数
 * @param {string} serviceName - 服务名称
 * @param {Object} serviceConfig - 服务配置
 * @returns {Object} axios实例
 */
const createServiceInstance = (serviceName, serviceConfig) => {
  const { baseUrl, prefix } = serviceConfig;

  // 开发环境使用代理路径，生产环境使用完整URL
  const instanceBaseURL = Settings.default.env.isDev ? '' : baseUrl;

  const instance = axios.create({
    baseURL: instanceBaseURL,
    timeout: Settings.default.api.timeout,
    withCredentials: Settings.default.api.withCredentials,
    headers: {
      "Content-Type": "application/json",
    },
  });

  let pending = []; // 存储每个请求的取消函数
  const CancelToken = axios.CancelToken;

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 开发环境下添加代理前缀
      if (Settings.default.env.isDev && prefix) {
        config.url = prefix + config.url;
      }

      // 添加认证令牌
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加服务标识头
      config.headers['X-Service-Name'] = serviceName;

      // 开发环境下打印请求信息
      if (Settings.default.env.isDev) {
        console.log(`🌐 [${serviceName.toUpperCase()}] API请求:`, config.method?.toUpperCase(), config.url);
      }

      // 添加请求取消令牌
      config.cancelToken = new CancelToken((c) => {
        pending.push({
          u: config.url + "&" + config.method + "&" + serviceName,
          f: c,
          service: serviceName
        });
      });

      return config;
    },
    (error) => {
      console.error(`❌ [${serviceName.toUpperCase()}] 请求拦截器错误:`, error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 移除已完成的请求
      removePending(response.config);

      // 开发环境下打印响应信息
      if (Settings.default.env.isDev) {
        console.log(`✅ [${serviceName.toUpperCase()}] API响应:`, response.status, response.config.url);
      }

      const responseData = response.data;

      // 处理认证相关的特殊code
      if (responseData.code === -210000 || responseData.code === 10008) {
        handleAuthRedirect(responseData);
        return;
      }

      // 统一判断业务code
      if (responseData.code !== undefined && responseData.code !== 200) {
        const errorMessage = responseData.msg || responseData.message || '请求失败';
        if (Settings.default.env.isDev) {
          console.error(`❌ [${serviceName.toUpperCase()}] 业务错误:`, responseData.code, errorMessage);
        }
        throw new Error(errorMessage);
      }

      return responseData;
    },
    (error) => {
      // 移除已完成的请求
      if (error.config) {
        removePending(error.config);
      }

      if (Settings.default.env.isDev) {
        console.error(`❌ [${serviceName.toUpperCase()}] API请求错误:`,
          error.response?.status, error.config?.url, error.message);
      }

      // 网络错误处理
      if (!error.response) {
        return Promise.reject(new Error(`${serviceName}服务连接失败，请检查网络`));
      }

      // HTTP状态码错误处理
      const status = error.response.status;
      let message = `${serviceName}服务请求失败`;

      switch (status) {
        case 401:
          message = '认证失败，请重新登录';
          break;
        case 403:
          message = '权限不足，无法访问该资源';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = `${serviceName}服务内部错误`;
          break;
        case 502:
        case 503:
        case 504:
          message = `${serviceName}服务暂时不可用`;
          break;
      }

      return Promise.reject(new Error(message));
    }
  );

  // 移除pending请求的辅助函数
  function removePending(config) {
    const flagUrl = config.url + "&" + config.method + "&" + serviceName;
    const index = pending.findIndex(item => item.u === flagUrl);
    if (index !== -1) {
      pending.splice(index, 1);
    }
  }

  // 处理认证重定向的辅助函数
  function handleAuthRedirect(responseData) {
    let redirectUrl;

    if (Settings.default.isDevelopMode) {
      redirectUrl = responseData.data.redirect +
        "?service=" +
        encodeURIComponent(
          Settings.default.developVerifyTicketUrl +
            "?from=" +
            encodeURIComponent(window.location.href)
        );
    } else {
      redirectUrl = responseData.data.redirect +
        "?service=" +
        encodeURIComponent(
          responseData.data.service +
            "?from=" +
            encodeURIComponent(window.location.href)
        );
    }

    const isChrome = window.navigator.userAgent.indexOf("Chrome") !== -1;
    if (!isChrome) {
      window.location.href = "./remind.html";
    } else {
      window.location.href = redirectUrl;
    }
  }

  return instance;
};

// 创建服务实例
const serviceInstances = {};
Object.entries(Settings.default.api.services).forEach(([serviceName, serviceConfig]) => {
  serviceInstances[serviceName] = createServiceInstance(serviceName, serviceConfig);
});

// 导出各服务的请求实例
export const oncallRequest = serviceInstances[SERVICE_TYPES.ONCALL];
export const systemRequest = serviceInstances[SERVICE_TYPES.SYSTEM];
export const messageRequest = serviceInstances[SERVICE_TYPES.MESSAGE];

// 默认导出oncall实例（保持向后兼容）
export default oncallRequest;

/**
 * 根据服务名称获取对应的请求实例
 * @param {string} serviceName - 服务名称 ('oncall', 'system', 'message')
 * @returns {Object} axios实例
 */
export function getServiceRequest(serviceName) {
  const instance = serviceInstances[serviceName];
  if (!instance) {
    throw new Error(`未找到服务 "${serviceName}" 的请求实例`);
  }
  return instance;
}

/**
 * 多服务API调用包装器
 * @param {string} serviceName - 服务名称
 * @param {Object} config - axios配置
 * @returns {Promise} API响应
 */
export function serviceApiCall(serviceName, config) {
  const instance = getServiceRequest(serviceName);
  return instance(config);
}

/**
 * 批量取消指定服务的所有pending请求
 * @param {string} serviceName - 服务名称，不传则取消所有服务的请求
 */
export function cancelServiceRequests(serviceName) {
  // 这个功能需要在实例创建时维护全局pending数组
  console.log(`取消 ${serviceName || '所有'} 服务的pending请求`);
}

/**
 * 获取服务健康状态
 * @param {string} serviceName - 服务名称
 * @returns {Promise<boolean>} 服务是否健康
 */
export async function checkServiceHealth(serviceName) {
  try {
    const instance = getServiceRequest(serviceName);
    await instance.get('/health');
    return true;
  } catch (error) {
    console.warn(`服务 ${serviceName} 健康检查失败:`, error.message);
    return false;
  }
}
