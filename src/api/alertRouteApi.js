import request from '@/utils/request';

/**
 * 获取告警路由列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.integrationId - 集成ID
 * @returns {Promise} API响应
 */
export function getAlertRouteList(params) {
  return request({
    url: '/api/v1/alert-routes/list',
    method: 'post',
    data: {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      integrationId: params.integrationId
    }
  });
}

/**
 * 创建告警路由
 * @param {Object} data - 路由数据
 * @returns {Promise} API响应
 */
export function createAlertRoute(data) {
  return request({
    url: '/api/v1/alert-routes',
    method: 'post',
    data
  });
}

/**
 * 更新告警路由
 * @param {number|string} id - 路由ID
 * @param {Object} data - 路由数据
 * @returns {Promise} API响应
 */
export function updateAlertRoute(id, data) {
  return request({
    url: `/api/v1/alert-routes/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除告警路由
 * @param {number|string} id - 路由ID
 * @returns {Promise} API响应
 */
export function deleteAlertRoute(id) {
  return request({
    url: `/api/v1/alert-routes/${id}`,
    method: 'delete'
  });
}

/**
 * 获取告警路由详情
 * @param {number|string} id - 路由ID
 * @returns {Promise} API响应
 */
export function getAlertRouteDetail(id) {
  return request({
    url: `/api/v1/alert-routes/${id}`,
    method: 'get'
  });
}
