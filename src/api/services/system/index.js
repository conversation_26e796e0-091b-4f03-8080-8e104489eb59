/**
 * System服务API接口
 * 系统管理相关的所有API调用
 * 端口: 2001
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
import { systemRequest, SERVICE_TYPES } from '@/utils/multiRequest'

// ==================== 认证相关接口 ====================

// 重新导出认证API
export * from './auth'

// ==================== 用户管理接口 ====================

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise<Object>} 用户列表
 */
export function getUserList(params) {
  return systemRequest({
    url: '/api/v1/system/users',
    method: 'post',
    data: params
  })
}

/**
 * 获取用户详情
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户详情
 */
export function getUserDetail(userId) {
  return systemRequest({
    url: `/api/v1/system/users/${userId}`,
    method: 'get'
  })
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @returns {Promise<Object>} 创建结果
 */
export function createUser(data) {
  return systemRequest({
    url: '/api/v1/system/users',
    method: 'post',
    data
  })
}

/**
 * 更新用户信息
 * @param {string} userId - 用户ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateUser(userId, data) {
  return systemRequest({
    url: `/api/v1/system/users/${userId}`,
    method: 'put',
    data
  })
}

/**
 * 删除用户
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteUser(userId) {
  return systemRequest({
    url: `/api/v1/system/users/${userId}`,
    method: 'delete'
  })
}

// ==================== 角色管理接口 ====================

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 角色列表
 */
export function getRoleList(params) {
  return systemRequest({
    url: '/api/v1/system/roles',
    method: 'post',
    data: params
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @returns {Promise<Object>} 创建结果
 */
export function createRole(data) {
  return systemRequest({
    url: '/api/v1/system/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {string} roleId - 角色ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateRole(roleId, data) {
  return systemRequest({
    url: `/api/v1/system/roles/${roleId}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {string} roleId - 角色ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteRole(roleId) {
  return systemRequest({
    url: `/api/v1/system/roles/${roleId}`,
    method: 'delete'
  })
}

// ==================== 权限管理接口 ====================

/**
 * 获取权限列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 权限列表
 */
export function getPermissionList(params) {
  return systemRequest({
    url: '/api/v1/system/permissions',
    method: 'post',
    data: params
  })
}

/**
 * 获取用户权限
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户权限
 */
export function getUserPermissions(userId) {
  return systemRequest({
    url: `/api/v1/system/users/${userId}/permissions`,
    method: 'get'
  })
}

/**
 * 分配用户权限
 * @param {string} userId - 用户ID
 * @param {Object} data - 权限数据
 * @returns {Promise<Object>} 分配结果
 */
export function assignUserPermissions(userId, data) {
  return systemRequest({
    url: `/api/v1/system/users/${userId}/permissions`,
    method: 'put',
    data
  })
}

// ==================== 组织管理接口 ====================

/**
 * 获取组织架构
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 组织架构
 */
export function getOrganization(params) {
  return systemRequest({
    url: '/api/v1/system/organization',
    method: 'post',
    data: params
  })
}

/**
 * 创建部门
 * @param {Object} data - 部门数据
 * @returns {Promise<Object>} 创建结果
 */
export function createDepartment(data) {
  return systemRequest({
    url: '/api/v1/system/departments',
    method: 'post',
    data
  })
}

/**
 * 更新部门
 * @param {string} deptId - 部门ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateDepartment(deptId, data) {
  return systemRequest({
    url: `/api/v1/system/departments/${deptId}`,
    method: 'put',
    data
  })
}

/**
 * 删除部门
 * @param {string} deptId - 部门ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteDepartment(deptId) {
  return systemRequest({
    url: `/api/v1/system/departments/${deptId}`,
    method: 'delete'
  })
}

// ==================== 系统配置接口 ====================

/**
 * 获取系统配置
 * @param {string} configKey - 配置键
 * @returns {Promise<Object>} 配置信息
 */
export function getSystemConfig(configKey) {
  return systemRequest({
    url: `/api/v1/system/config/${configKey}`,
    method: 'get'
  })
}

/**
 * 更新系统配置
 * @param {string} configKey - 配置键
 * @param {Object} data - 配置数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateSystemConfig(configKey, data) {
  return systemRequest({
    url: `/api/v1/system/config/${configKey}`,
    method: 'put',
    data
  })
}

// ==================== 审计日志接口 ====================

/**
 * 获取审计日志
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @param {string} params.userId - 用户ID
 * @param {string} params.action - 操作类型
 * @returns {Promise<Object>} 审计日志
 */
export function getAuditLogs(params) {
  return systemRequest({
    url: '/api/v1/system/audit-logs',
    method: 'post',
    data: params
  })
}

/**
 * 导出审计日志
 * @param {Object} params - 导出参数
 * @returns {Promise<Object>} 导出结果
 */
export function exportAuditLogs(params) {
  return systemRequest({
    url: '/api/v1/system/audit-logs/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// ==================== 系统监控接口 ====================

/**
 * 获取系统状态
 * @returns {Promise<Object>} 系统状态
 */
export function getSystemStatus() {
  return systemRequest({
    url: '/api/v1/system/status',
    method: 'get'
  })
}

/**
 * 获取系统指标
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 系统指标
 */
export function getSystemMetrics(params) {
  return systemRequest({
    url: '/api/v1/system/metrics',
    method: 'post',
    data: params
  })
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API响应
 */
export async function getSystemDataWithErrorHandling(params) {
  try {
    const response = await getUserList(params)
    console.log('✅ System数据获取成功:', response)
    return response
  } catch (error) {
    console.error('❌ System数据获取失败:', error.message)
    throw error
  }
}
