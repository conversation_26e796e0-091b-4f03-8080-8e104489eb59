import * as realApi from './alertRouteApi';

/**
 * 获取告警路由列表（适配器）
 * @param {string} integrationId - 集成ID
 * @returns {Promise} 适配后的数据
 */
export function getRoutingRules(integrationId) {
  return realApi.getAlertRouteList({
    page: 1,
    pageSize: 100, // 获取所有路由规则
    integrationId
  }).then(response => {
    console.log('🔍 告警路由API响应:', response);

    if (response && response.data && response.data.data && response.data.data.length > 0) {
      // 取第一个路由配置（通常一个集成只有一个路由配置）
      const routeData = response.data.data[0];

      // 转换API数据格式到组件期望的格式
      const adaptedData = {
        id: routeData.id,
        integrationId: routeData.integrationId,
        cases: routeData.cases.map(caseItem => ({
          if: caseItem.if,
          channel_ids: caseItem.channelIds, // API使用channelIds，组件期望channel_ids
          fallthrough: caseItem.fallthrough
        })),
        default: {
          channel_ids: routeData.default.channelIds // API使用channelIds，组件期望channel_ids
        },
        status: routeData.status,
        version: routeData.version,
        updated_by: routeData.updatedBy,
        creator_id: routeData.creatorId,
        created_at: routeData.createdAt,
        updated_at: routeData.updatedAt
      };

      console.log('🔄 适配后的路由数据:', adaptedData);

      return {
        success: true,
        data: adaptedData
      };
    } else {
      // 如果没有路由配置，返回默认结构
      return {
        success: true,
        data: {
          integrationId: integrationId,
          cases: [],
          default: {
            channel_ids: []
          },
          status: 'enabled',
          version: 1,
          updated_by: 1,
          creator_id: 1,
          created_at: Math.floor(Date.now() / 1000),
          updated_at: Math.floor(Date.now() / 1000)
        }
      };
    }
  }).catch(error => {
    console.error('获取告警路由失败:', error);
    throw error;
  });
}

/**
 * 更新告警路由规则（适配器）
 * @param {string} integrationId - 集成ID
 * @param {Object} routingRules - 路由规则数据
 * @returns {Promise} 适配后的响应
 */
export function updateRoutingRules(integrationId, routingRules) {
  // 转换组件数据格式到API期望的格式
  const apiData = {
    id: routingRules.id ? routingRules.id.toString() : "1", // 确保ID为字符串
    integrationId: integrationId,
    cases: routingRules.cases.map(caseItem => ({
      if: caseItem.if,
      channelIds: caseItem.channel_ids, // 组件使用channel_ids，API期望channelIds
      fallthrough: caseItem.fallthrough
    })),
    default: {
      channelIds: routingRules.default.channel_ids // 组件使用channel_ids，API期望channelIds
    },
    status: routingRules.status || 'enabled',
    version: (routingRules.version || 1) + 1, // 版本号递增
    updatedBy: routingRules.updated_by || 1,
    creatorId: routingRules.creator_id || 1
  };

  console.log('📤 发送到API的路由数据:', apiData);

  // 统一使用PUT接口更新，无论是添加、删除还是修改操作
  const routeId = routingRules.id || "1"; // 如果没有ID，使用默认ID "1"
  return realApi.updateAlertRoute(routeId, apiData).then(response => {
    console.log('✅ 更新路由响应:', response);
    return {
      success: response.code === 200,
      data: response.data
    };
  }).catch(error => {
    console.error('❌ 更新路由失败:', error);
    throw error;
  });
}

/**
 * 获取通知渠道列表（模拟数据，实际应该从真实API获取）
 * @returns {Promise} 渠道列表
 */
export function getChannels() {
  // 这里应该调用真实的渠道API，暂时返回模拟数据
  const mockChannels = [
    { id: 1001, name: '开发团队', type: '钉钉群' },
    { id: 1002, name: '运维团队', type: '企业微信群' },
    { id: 1003, name: '产品团队', type: '飞书群' },
    { id: 1004, name: '测试团队', type: '钉钉群' },
    { id: 1005, name: '安全团队', type: '邮件组' },
    { id: 1006, name: '默认通知组', type: '企业微信群' }
  ];

  return Promise.resolve({
    success: true,
    data: mockChannels
  });
}

// 默认导出
export default {
  getRoutingRules,
  updateRoutingRules,
  getChannels
};
