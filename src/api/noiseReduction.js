// 导入模拟数据
import {
  getAlarmAggregationConfig,
  updateAlarmAggregationConfig,
  getAlarmSuppressionConfig,
  updateAlarmSuppressionConfig,
  getSilentStrategies,
  createSilentStrategy,
  updateSilentStrategy,
  deleteSilentStrategy
} from './mock/noiseReduction';

// 获取告警聚合配置
export function fetchAlarmAggregationConfig(spaceId) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/aggregation`,
  //   method: 'get'
  // })
  
  // 使用模拟数据
  return Promise.resolve(getAlarmAggregationConfig(spaceId));
}

// 更新告警聚合配置
export function updateAlarmAggregation(spaceId, data) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/aggregation`,
  //   method: 'put',
  //   data
  // })
  
  // 使用模拟数据
  return Promise.resolve(updateAlarmAggregationConfig({ ...data, space_id: spaceId }));
}

// 获取故障收敛配置
export function fetchAlarmSuppressionConfig(spaceId) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/suppression`,
  //   method: 'get'
  // })
  
  // 使用模拟数据
  return Promise.resolve(getAlarmSuppressionConfig(spaceId));
}

// 更新故障收敛配置
export function updateAlarmSuppression(spaceId, data) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/suppression`,
  //   method: 'put',
  //   data
  // })
  
  // 使用模拟数据
  return Promise.resolve(updateAlarmSuppressionConfig({ ...data, space_id: spaceId }));
}

// 获取静默策略列表
export function fetchSilentStrategies(spaceId) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/silent-strategies`,
  //   method: 'get'
  // })
  
  // 使用模拟数据
  return Promise.resolve(getSilentStrategies(spaceId));
}

// 创建静默策略
export function createSilentStrategyApi(spaceId, data) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/silent-strategies`,
  //   method: 'post',
  //   data
  // })
  
  // 使用模拟数据
  return Promise.resolve(createSilentStrategy({ ...data, space_id: spaceId }));
}

// 更新静默策略
export function updateSilentStrategyApi(spaceId, strategyId, data) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/silent-strategies/${strategyId}`,
  //   method: 'put',
  //   data
  // })
  
  // 使用模拟数据
  return Promise.resolve(updateSilentStrategy({ ...data, id: strategyId, space_id: spaceId }));
}

// 删除静默策略
export function deleteSilentStrategyApi(spaceId, strategyId) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/spaces/${spaceId}/noise-reduction/silent-strategies/${strategyId}`,
  //   method: 'delete'
  // })
  
  // 使用模拟数据
  return Promise.resolve(deleteSilentStrategy(strategyId));
}
