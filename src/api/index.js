/**
 * API统一导出文件
 * 提供清晰的API接口访问入口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// ==================== 服务API导出 ====================

// OnCall服务 - 值班管理、集成管理、告警路由
export * as oncallApi from './services/oncall'

// System服务 - 系统管理、用户权限
export * as systemApi from './services/system'

// Message服务 - 消息通知、模板管理
export * as messageApi from './services/message'

// ==================== 适配器导出 ====================

// 集成管理适配器 (混合Mock和真实API)
export * as integrationAdapter from './adapters/integration'

// 告警路由适配器
export * as alertRouteAdapter from './adapters/alertRoute'

// ==================== 遗留API导出 ====================

// 权限系统API
export * as techApi from './legacy/techApi'

// 工作空间管理 (遗留)
export * as spaceManagementApi from './legacy/spaceManagement'

// 模板管理 (遗留)
export * as templateManagementApi from './legacy/templateManagement'

// 降噪配置 (遗留)
export * as noiseReductionApi from './legacy/noiseReduction'

// ==================== Mock数据导出 ====================

// Mock数据统一导出
export * as mockApi from './mock'

// ==================== 便捷导出 ====================

// 常用API的便捷导出
export { 
  // OnCall服务常用API
  getOncallSchedules,
  createOncallSchedule,
  getOncallUsers,
  getCurrentOncallUser,
  
  // 集成管理常用API
  getIntegrationList,
  createIntegration,
  getIntegrationTypes,
  
  // 告警路由常用API
  getAlertRouteList,
  createAlertRoute,
  updateAlertRoute
} from './services/oncall'

export {
  // System服务常用API
  getUserList,
  getRoleList,
  getSystemConfig
} from './services/system'

export {
  // Message服务常用API
  sendMessage,
  getMessageTemplates,
  getNotificationChannels
} from './services/message'

// ==================== 服务健康检查 ====================

import { checkServiceHealth, SERVICE_TYPES } from '@/utils/multiRequest'

/**
 * 检查所有服务健康状态
 * @returns {Promise<Object>} 各服务健康状态
 */
export async function checkAllServicesHealth() {
  const results = {}
  
  for (const [serviceName, serviceType] of Object.entries(SERVICE_TYPES)) {
    try {
      results[serviceName] = await checkServiceHealth(serviceType)
    } catch (error) {
      results[serviceName] = false
      console.warn(`服务 ${serviceName} 健康检查失败:`, error.message)
    }
  }
  
  return results
}

// ==================== API使用指南 ====================

/**
 * 打印API使用指南
 */
export function printApiGuide() {
  console.group('📚 ZeroDuty API使用指南')
  
  console.log('🔧 服务API:')
  console.log('  - oncallApi: 值班管理、集成管理、告警路由')
  console.log('  - systemApi: 系统管理、用户权限')
  console.log('  - messageApi: 消息通知、模板管理')
  
  console.log('🔄 适配器:')
  console.log('  - integrationAdapter: 集成管理适配器')
  console.log('  - alertRouteAdapter: 告警路由适配器')
  
  console.log('📁 遗留API:')
  console.log('  - techApi: 权限系统')
  console.log('  - spaceManagementApi: 工作空间管理')
  console.log('  - templateManagementApi: 模板管理')
  
  console.log('🎭 Mock数据:')
  console.log('  - mockApi: 所有Mock数据')
  
  console.log('💡 使用示例:')
  console.log('  import { oncallApi, systemApi } from "@/api"')
  console.log('  import { getOncallSchedules } from "@/api"')
  console.log('  import { integrationAdapter } from "@/api"')
  
  console.groupEnd()
}

// ==================== 默认导出 ====================

export default {
  // 服务API
  oncallApi,
  systemApi,
  messageApi,
  
  // 适配器
  integrationAdapter,
  alertRouteAdapter,
  
  // 遗留API
  techApi,
  spaceManagementApi,
  templateManagementApi,
  noiseReductionApi,
  
  // Mock数据
  mockApi,
  
  // 工具函数
  checkAllServicesHealth,
  printApiGuide
}
