/**
 * OnCall服务API接口
 * 值班管理相关的所有API调用
 * 端口: 2002
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
import { oncallRequest, SERVICE_TYPES } from '@/utils/multiRequest'

// ==================== 值班管理接口 ====================

/**
 * 获取值班计划列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.spaceId - 工作空间ID
 * @returns {Promise<Object>} 值班计划列表
 */
export function getOncallSchedules(params) {
  return oncallRequest({
    url: '/api/v1/oncall/schedules',
    method: 'post',
    data: params
  })
}

/**
 * 创建值班计划
 * @param {Object} data - 值班计划数据
 * @returns {Promise<Object>} 创建结果
 */
export function createOncallSchedule(data) {
  return oncallRequest({
    url: '/api/v1/oncall/schedules',
    method: 'post',
    data
  })
}

/**
 * 更新值班计划
 * @param {string} id - 值班计划ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateOncallSchedule(id, data) {
  return oncallRequest({
    url: `/api/v1/oncall/schedules/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除值班计划
 * @param {string} id - 值班计划ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteOncallSchedule(id) {
  return oncallRequest({
    url: `/api/v1/oncall/schedules/${id}`,
    method: 'delete'
  })
}

// ==================== 值班人员接口 ====================

/**
 * 获取值班人员列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 值班人员列表
 */
export function getOncallUsers(params) {
  return oncallRequest({
    url: '/api/v1/oncall/users',
    method: 'post',
    data: params
  })
}

/**
 * 添加值班人员
 * @param {Object} data - 人员数据
 * @returns {Promise<Object>} 添加结果
 */
export function addOncallUser(data) {
  return oncallRequest({
    url: '/api/v1/oncall/users',
    method: 'post',
    data
  })
}

/**
 * 移除值班人员
 * @param {string} userId - 用户ID
 * @param {string} scheduleId - 值班计划ID
 * @returns {Promise<Object>} 移除结果
 */
export function removeOncallUser(userId, scheduleId) {
  return oncallRequest({
    url: `/api/v1/oncall/users/${userId}`,
    method: 'delete',
    data: { scheduleId }
  })
}

// ==================== 值班轮换接口 ====================

/**
 * 获取值班轮换规则
 * @param {string} scheduleId - 值班计划ID
 * @returns {Promise<Object>} 轮换规则
 */
export function getRotationRules(scheduleId) {
  return oncallRequest({
    url: `/api/v1/oncall/schedules/${scheduleId}/rotations`,
    method: 'get'
  })
}

/**
 * 更新值班轮换规则
 * @param {string} scheduleId - 值班计划ID
 * @param {Object} data - 轮换规则数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateRotationRules(scheduleId, data) {
  return oncallRequest({
    url: `/api/v1/oncall/schedules/${scheduleId}/rotations`,
    method: 'put',
    data
  })
}

// ==================== 值班覆盖接口 ====================

/**
 * 获取值班覆盖列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 覆盖列表
 */
export function getOncallOverrides(params) {
  return oncallRequest({
    url: '/api/v1/oncall/overrides',
    method: 'post',
    data: params
  })
}

/**
 * 创建值班覆盖
 * @param {Object} data - 覆盖数据
 * @returns {Promise<Object>} 创建结果
 */
export function createOncallOverride(data) {
  return oncallRequest({
    url: '/api/v1/oncall/overrides',
    method: 'post',
    data
  })
}

/**
 * 删除值班覆盖
 * @param {string} id - 覆盖ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteOncallOverride(id) {
  return oncallRequest({
    url: `/api/v1/oncall/overrides/${id}`,
    method: 'delete'
  })
}

// ==================== 值班统计接口 ====================

/**
 * 获取值班统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.spaceId - 工作空间ID
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise<Object>} 统计数据
 */
export function getOncallStatistics(params) {
  return oncallRequest({
    url: '/api/v1/oncall/statistics',
    method: 'post',
    data: params
  })
}

/**
 * 获取当前值班人员
 * @param {string} spaceId - 工作空间ID
 * @returns {Promise<Object>} 当前值班人员信息
 */
export function getCurrentOncallUser(spaceId) {
  return oncallRequest({
    url: `/api/v1/oncall/current/${spaceId}`,
    method: 'get'
  })
}

// ==================== 值班日历接口 ====================

/**
 * 获取值班日历数据
 * @param {Object} params - 查询参数
 * @param {string} params.spaceId - 工作空间ID
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise<Object>} 日历数据
 */
export function getOncallCalendar(params) {
  return oncallRequest({
    url: '/api/v1/oncall/calendar',
    method: 'post',
    data: params
  })
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API响应
 */
export async function getOncallDataWithErrorHandling(params) {
  try {
    const response = await getOncallSchedules(params)
    console.log('✅ OnCall数据获取成功:', response)
    return response
  } catch (error) {
    console.error('❌ OnCall数据获取失败:', error.message)
    
    // 可以在这里添加降级逻辑
    // 比如返回缓存数据或默认数据
    throw error
  }
}
