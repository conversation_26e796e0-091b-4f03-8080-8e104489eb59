// import request from '@/utils/request'
import { templateList, teamList } from './mock/templateManagement'

// 获取模板列表
export function getTemplateList() {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: '/api/template/list',
  //   method: 'get'
  // })
  
  // mock数据
  return Promise.resolve({
    code: 200,
    data: templateList,
    message: '获取成功'
  })
}

// 获取模板详情
export function getTemplateDetail(id) {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/template/detail/${id}`,
  //   method: 'get'
  // })
  
  // mock数据
  const template = templateList.find(item => item.id === Number(id))
  
  if (template) {
    const teamInfo = teamList.find(team => team.id === template.teamId)
    if (teamInfo) {
      template.teamName = teamInfo.name
    }
  }
  
  return Promise.resolve({
    code: 200,
    data: template || null,
    message: template ? '获取成功' : '模板不存在'
  })
}

// 获取团队列表
export function getTeamList() {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: '/api/team/list',
  //   method: 'get'
  // })
  
  // mock数据
  return Promise.resolve({
    code: 200,
    data: teamList,
    message: '获取成功'
  })
}

// 创建模板
export function createTemplate() {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: '/api/template/create',
  //   method: 'post',
  //   data
  // })
  
  // mock数据
  return Promise.resolve({
    code: 200,
    data: null,
    message: '创建成功'
  })
}

// 更新模板
export function updateTemplate() {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: '/api/template/update',
  //   method: 'put',
  //   data
  // })
  
  // mock数据
  return Promise.resolve({
    code: 200,
    data: null,
    message: '更新成功'
  })
}

// 删除模板
export function deleteTemplate() {
  // 实际项目中应该是向后端请求数据
  // return request({
  //   url: `/api/template/delete/${id}`,
  //   method: 'delete'
  // })
  
  // mock数据
  return Promise.resolve({
    code: 200,
    data: null,
    message: '删除成功'
  })
} 