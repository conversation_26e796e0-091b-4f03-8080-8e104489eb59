/**
 * 多API服务示例
 * 展示如何使用不同的API服务器获取数据
 */
import { primaryRequest, secondaryRequest, thirdRequest, multiApiCall } from '@/utils/multiRequest'

// ==================== 使用主要API服务器 ====================

/**
 * 从主要API服务器获取用户信息
 */
export function getUserInfo() {
  return primaryRequest({
    url: '/api/v1/user/info',
    method: 'get'
  })
}

/**
 * 从主要API服务器获取工作空间列表
 */
export function getSpaceList(params) {
  return primaryRequest({
    url: '/api/v1/spaces/list',
    method: 'post',
    data: params
  })
}

// ==================== 使用第二个API服务器 ====================

/**
 * 从第二个API服务器获取监控数据
 */
export function getMonitoringData(params) {
  return secondaryRequest({
    url: '/api/v1/monitoring/metrics',
    method: 'post',
    data: params
  })
}

/**
 * 从第二个API服务器获取告警数据
 */
export function getAlertData(params) {
  return secondaryRequest({
    url: '/api/v1/alerts/list',
    method: 'post',
    data: params
  })
}

// ==================== 使用第三个API服务器 ====================

/**
 * 从第三个API服务器获取日志数据
 */
export function getLogData(params) {
  return thirdRequest({
    url: '/api/v1/logs/search',
    method: 'post',
    data: params
  })
}

/**
 * 从第三个API服务器获取分析报告
 */
export function getAnalyticsReport(params) {
  return thirdRequest({
    url: '/api/v1/analytics/report',
    method: 'post',
    data: params
  })
}

// ==================== 使用动态API选择 ====================

/**
 * 动态选择API服务器获取数据
 * @param {string} serviceName - 服务名称 ('primary', 'secondary', 'third')
 * @param {string} endpoint - API端点
 * @param {Object} params - 请求参数
 */
export function getDynamicData(serviceName, endpoint, params) {
  return multiApiCall(serviceName, {
    url: endpoint,
    method: 'post',
    data: params
  })
}

// ==================== 开发环境代理路径示例 ====================

/**
 * 开发环境下使用代理路径的示例
 * 这些函数在开发环境下会通过Vite代理转发到对应的后端服务器
 */

// 使用 /api 代理（转发到 localhost:2002）
export function getDataFromApi1(params) {
  return primaryRequest({
    url: '/api/v1/data',  // 开发环境: /api -> localhost:2002
    method: 'post',
    data: params
  })
}

// 使用 /api2 代理（转发到 localhost:3003）
export function getDataFromApi2(params) {
  return secondaryRequest({
    url: '/api2/v1/data',  // 开发环境: /api2 -> localhost:3003
    method: 'post',
    data: params
  })
}

// 使用 /api3 代理（转发到 localhost:4004）
export function getDataFromApi3(params) {
  return thirdRequest({
    url: '/api3/v1/data',  // 开发环境: /api3 -> localhost:4004
    method: 'post',
    data: params
  })
}

// ==================== 错误处理示例 ====================

/**
 * 带错误处理的API调用示例
 */
export async function getDataWithErrorHandling(serviceName, endpoint, params) {
  try {
    const response = await multiApiCall(serviceName, {
      url: endpoint,
      method: 'post',
      data: params
    })
    
    // 成功处理
    console.log(`✅ 从 ${serviceName} 服务获取数据成功:`, response)
    return response
    
  } catch (error) {
    // 错误处理
    console.error(`❌ 从 ${serviceName} 服务获取数据失败:`, error.message)
    
    // 可以在这里添加降级逻辑，比如尝试其他服务器
    if (serviceName === 'secondary') {
      console.log('🔄 尝试使用主要服务器...')
      return getDataWithErrorHandling('primary', endpoint, params)
    }
    
    throw error
  }
}

// ==================== 批量请求示例 ====================

/**
 * 从多个API服务器批量获取数据
 */
export async function getBatchData(requests) {
  const promises = requests.map(({ serviceName, endpoint, params }) => {
    return multiApiCall(serviceName, {
      url: endpoint,
      method: 'post',
      data: params
    }).catch(error => {
      // 单个请求失败不影响其他请求
      console.error(`批量请求失败 - ${serviceName}:`, error.message)
      return { error: error.message, serviceName }
    })
  })
  
  const results = await Promise.all(promises)
  return results
}

// ==================== 使用示例 ====================

/**
 * 使用示例函数
 */
export async function exampleUsage() {
  try {
    // 1. 从不同服务器获取数据
    const userInfo = await getUserInfo()
    const monitoringData = await getMonitoringData({ timeRange: '1h' })
    const logData = await getLogData({ query: 'error', limit: 100 })
    
    // 2. 动态选择服务器
    const dynamicData = await getDynamicData('secondary', '/api/v1/custom', { id: 123 })
    
    // 3. 批量请求
    const batchResults = await getBatchData([
      { serviceName: 'primary', endpoint: '/api/v1/spaces', params: {} },
      { serviceName: 'secondary', endpoint: '/api/v1/metrics', params: {} },
      { serviceName: 'third', endpoint: '/api/v1/logs', params: {} }
    ])
    
    console.log('所有数据获取完成:', {
      userInfo,
      monitoringData,
      logData,
      dynamicData,
      batchResults
    })
    
  } catch (error) {
    console.error('数据获取失败:', error)
  }
}
