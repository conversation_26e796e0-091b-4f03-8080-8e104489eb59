 const settings  =  {
  /**
   * 项目名称
   */
  projectTitle: "zero_duty_tcvue",
  /**
   * 项目名称
   */
  projectName: "zero_duty_tcvue",
  /**
   * 当前的系统模式是否是开发模式
   * 影响请求权限校验机制*/
  isDevelopMode: false,
  /**
   * 开发模式下的请求校验服务地址
   */
  developVerifyTicketUrl: "http://localhost:9008/tech/verifyTicket",
  /**
   * 布局模式 horizontal（菜单栏在上部） vertical （菜单栏在左边） none（不用默认布局）
   */
  layoutMode: "vertical",
  /**
   * SSO服务地址*/
  ssoServerUrl: "https://internet-xsjsso.seasungame.com/api-xsjsso",

  /**
   * 是否启用事件总线（因为事件总线往往不是必须的，且容易影响性能所以不在必须的情况下不启动）
   */
  useBus: true,
  /**
   * 埋点数据上报的地址 (需要联系技术中心 获取地址)
   */
  logsServerUrl: "/client/push/app_200001006/",
  /**
   *  埋点上传的表名
   */
  logsTableName: "tc_vue_base_template",

  /**
   *  是否使用mock数据
   *  开发阶段: true (使用Mock数据)
   *  联调阶段: false (使用代理到后端)
   *  生产环境: false (使用真实API)
   */
  useMock: import.meta.env.VITE_USE_MOCK === 'true' || false,

  /**
   * API服务器配置
   */
  api: {
    // 请求超时时间 (毫秒)
    timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,

    // 是否携带凭证
    withCredentials: true,

    // 多服务配置
    services: {
      // OnCall服务 - 值班管理相关API
      oncall: {
        baseUrl: import.meta.env.VITE_ONCALL_API_BASE_URL || (
          import.meta.env.MODE === 'production'
            ? 'https://oncall-api.zeroduty.com' // 生产环境OnCall API地址
            : '' // 开发环境使用代理路径 /oncall-api
        ),
        prefix: '/oncall-api'
      },

      // System服务 - 系统管理相关API
      system: {
        baseUrl: import.meta.env.VITE_SYSTEM_API_BASE_URL || (
          import.meta.env.MODE === 'production'
            ? 'https://system-api.zeroduty.com' // 生产环境System API地址
            : '' // 开发环境使用代理路径 /system-api
        ),
        prefix: '/system-api'
      },

      // Message服务 - 消息通知相关API
      message: {
        baseUrl: import.meta.env.VITE_MESSAGE_API_BASE_URL || (
          import.meta.env.MODE === 'production'
            ? 'https://message-api.zeroduty.com' // 生产环境Message API地址
            : '' // 开发环境使用代理路径 /message-api
        ),
        prefix: '/message-api'
      }
    }
  },

  /**
   * 环境配置
   */
  env: {
    // 当前环境: development | staging | production
    current: import.meta.env.MODE || 'development',

    // 是否为开发环境
    isDev: import.meta.env.MODE === 'development',

    // 是否为生产环境
    isProd: import.meta.env.MODE === 'production',

  },

  /**
   *  是否启用菜单权限控制
   */
  userPrivileges: true,
  /**
   * main服务地址(登出后再登录的默认地址)*/
  mainServerUrl: "https://tech.seasungame.com",
};


 export default settings;
